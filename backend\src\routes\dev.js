const Router = require('koa-router');
const { createTables, insertSampleData } = require('../config/database');

const router = new Router();

// POST /api/v1/dev/init-database - 数据库初始化API (开发专用)
router.post('/init-database', async (ctx) => {
  try {
    // 检查是否为开发环境
    if (process.env.NODE_ENV === 'production') {
      ctx.status = 403;
      ctx.body = {
        success: false,
        code: 403,
        message: '禁止访问',
        error: '此接口仅在开发环境可用',
        timestamp: new Date().toISOString()
      };
      return;
    }

    console.log('开始初始化数据库...');
    
    // 创建数据库表结构
    const tablesCreated = createTables();
    
    // 插入示例数据
    const sampleDataInserted = insertSampleData();
    
    console.log('数据库初始化完成');
    
    ctx.status = 200;
    ctx.body = {
      success: true,
      code: 200,
      message: '数据库初始化成功',
      data: {
        tables_created: ['subjects', 'file_nodes', 'operation_logs'],
        sample_data_inserted: sampleDataInserted,
        indexes_created: [
          'idx_subjects_name',
          'idx_subjects_created_at',
          'idx_file_nodes_subject_id',
          'idx_file_nodes_parent_id',
          'idx_file_nodes_type',
          'idx_file_nodes_path',
          'idx_operation_logs_type',
          'idx_operation_logs_created_at'
        ]
      },
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('数据库初始化失败:', error);
    
    ctx.status = 500;
    ctx.body = {
      success: false,
      code: 500,
      message: '数据库初始化失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
});

// GET /api/v1/dev/database-status - 获取数据库状态 (开发专用)
router.get('/database-status', async (ctx) => {
  try {
    // 检查是否为开发环境
    if (process.env.NODE_ENV === 'production') {
      ctx.status = 403;
      ctx.body = {
        success: false,
        code: 403,
        message: '禁止访问',
        error: '此接口仅在开发环境可用',
        timestamp: new Date().toISOString()
      };
      return;
    }

    const { getDatabase } = require('../config/database');
    const db = getDatabase();
    
    // 获取表信息
    const tables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `).all();
    
    // 获取各表的记录数
    const tableStats = {};
    for (const table of tables) {
      const count = db.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
      tableStats[table.name] = count.count;
    }
    
    ctx.status = 200;
    ctx.body = {
      success: true,
      code: 200,
      message: '获取数据库状态成功',
      data: {
        tables: tables.map(t => t.name),
        table_stats: tableStats,
        database_file: process.env.DATABASE_PATH || 'data/database.sqlite'
      },
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('获取数据库状态失败:', error);
    
    ctx.status = 500;
    ctx.body = {
      success: false,
      code: 500,
      message: '获取数据库状态失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
});

module.exports = router;
