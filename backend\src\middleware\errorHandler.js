// 全局错误处理中间件
const errorHandler = async (ctx, next) => {
  try {
    await next();
  } catch (err) {
    // 记录错误日志
    console.error('API Error:', {
      method: ctx.method,
      path: ctx.path,
      error: err.message,
      stack: err.stack,
      timestamp: new Date().toISOString()
    });

    // 设置HTTP状态码
    ctx.status = err.status || err.statusCode || 500;

    // 根据错误类型设置响应
    let errorMessage = '服务器内部错误';
    let errorDetail = '系统发生未知错误';

    // 处理不同类型的错误
    switch (ctx.status) {
      case 400:
        errorMessage = '请求参数错误';
        errorDetail = err.message || '请求格式不正确';
        break;
      case 401:
        errorMessage = '未授权访问';
        errorDetail = err.message || '需要身份验证';
        break;
      case 403:
        errorMessage = '禁止访问';
        errorDetail = err.message || '没有访问权限';
        break;
      case 404:
        errorMessage = '资源不存在';
        errorDetail = err.message || '请求的资源未找到';
        break;
      case 409:
        errorMessage = '资源冲突';
        errorDetail = err.message || '资源已存在或冲突';
        break;
      case 413:
        errorMessage = '请求体过大';
        errorDetail = err.message || '上传的文件或数据过大';
        break;
      case 415:
        errorMessage = '不支持的媒体类型';
        errorDetail = err.message || '请求的内容类型不支持';
        break;
      case 422:
        errorMessage = '请求参数验证失败';
        errorDetail = err.message || '提交的数据格式不正确';
        break;
      case 429:
        errorMessage = '请求过于频繁';
        errorDetail = err.message || '请求次数超出限制';
        break;
      case 500:
      default:
        errorMessage = '服务器内部错误';
        errorDetail = process.env.NODE_ENV === 'development' ? err.message : '系统发生内部错误';
        break;
    }

    // 统一错误响应格式
    ctx.body = {
      success: false,
      code: ctx.status,
      message: errorMessage,
      error: errorDetail,
      timestamp: new Date().toISOString()
    };

    // 在开发环境下，添加更多调试信息
    if (process.env.NODE_ENV === 'development') {
      ctx.body.debug = {
        stack: err.stack,
        method: ctx.method,
        path: ctx.path,
        query: ctx.query,
        body: ctx.request.body
      };
    }

    // 设置响应头
    ctx.type = 'application/json';
  }
};

module.exports = errorHandler;
