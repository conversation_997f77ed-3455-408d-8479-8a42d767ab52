// 请求日志中间件
const logger = async (ctx, next) => {
  const start = Date.now();
  
  // 记录请求开始
  console.log(`→ ${ctx.method} ${ctx.path} - ${new Date().toISOString()}`);
  
  try {
    await next();
  } catch (error) {
    // 错误会被errorHandler处理，这里只记录
    throw error;
  } finally {
    const duration = Date.now() - start;
    
    // 记录请求完成
    const logData = {
      method: ctx.method,
      path: ctx.path,
      status: ctx.status,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString()
    };
    
    // 根据状态码使用不同的日志级别
    if (ctx.status >= 500) {
      console.error(`← ${ctx.method} ${ctx.path} ${ctx.status} - ${duration}ms [ERROR]`);
    } else if (ctx.status >= 400) {
      console.warn(`← ${ctx.method} ${ctx.path} ${ctx.status} - ${duration}ms [WARN]`);
    } else {
      console.log(`← ${ctx.method} ${ctx.path} ${ctx.status} - ${duration}ms [OK]`);
    }
    
    // 慢请求警告
    if (duration > 3000) {
      console.warn(`⚠️  慢请求检测: ${ctx.method} ${ctx.path} 耗时 ${duration}ms`);
    }
  }
};

module.exports = logger;
