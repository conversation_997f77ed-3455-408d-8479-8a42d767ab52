{"name": "term-review-frontend", "version": "1.0.0", "description": "期末复习平台前端应用", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "keywords": ["vue3", "typescript", "term-review", "education"], "author": "<PERSON> (工程师)", "license": "MIT", "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "ant-design-vue": "^4.0.8", "@ant-design/icons-vue": "^7.0.1", "axios": "^1.6.2", "marked": "^12.0.0", "highlight.js": "^11.9.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.5.1", "typescript": "~5.3.0", "vue-tsc": "^1.8.25", "vite": "^5.0.10", "vitest": "^1.0.4", "jsdom": "^23.0.1", "@vitest/coverage-v8": "^1.0.4", "@unocss/vite": "^0.58.0", "unocss": "^0.58.0", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint-plugin-vue": "^9.19.2"}, "engines": {"node": ">=18.0.0"}}