# Sprint-3 详细任务计划 - 访客浏览核心体验

## 文档信息

| 项目 | 内容 |
|------|------|
| **Sprint名称** | Sprint-3: 访客浏览核心体验 |
| **任务ID** | `2270f9a6-66ec-4877-8420-b93331d26975` |
| **优先级** | P0（最高优先级） |
| **预估工期** | 3-4天 |
| **负责人** | Alex (工程师) |
| **依赖任务** | Sprint-2: 学科管理完整功能 |
| **文档版本** | v1.0.0 |
| **创建日期** | 2025-01-08 |
| **规划负责人** | Mike (团队领袖) & Bob (架构师) |
| **状态** | 待执行 |

## Sprint概述

基于《PRD_期末复习平台_v1.0.md》、《Overall_Architecture_期末复习平台.md》和《Task_Planning_期末复习平台_v1.1.md》，将"访客浏览核心体验"切片分解为5个具体的、可执行的子任务。实现访客首页、学科展示、文件结构浏览和基础Markdown内容渲染，支持响应式设计，适配多种设备，实现文件夹优先的排序逻辑。

## 核心业务流程

**访客浏览流程**：
```
访问首页 → 查看学科列表 → 选择学科 → 浏览文件结构 → 选择文件 → 阅读内容
```

## 核心业务规则

1. **内容开放性**：所有内容对访客开放，无需登录
2. **排序规则**：文件夹优先，按字母排序
3. **内容渲染**：Markdown内容正确渲染，支持代码高亮
4. **图片显示**：图片通过相对路径正确显示，支持懒加载
5. **响应式设计**：适配桌面、平板、手机等各种设备
6. **性能要求**：页面加载时间 < 3秒，支持100个并发访客

## 核心设计原则

1. **API契约优先**：所有开发工作以API契约为"法律合同"
2. **用户体验优先**：提供流畅的浏览体验和清晰的导航
3. **性能优化**：懒加载、缓存策略、响应时间优化
4. **错误处理**：友好的错误提示和异常处理

---

## 任务3.0: API契约设计与评审 (API Contract Design & Review)

**优先级**: P-1 (最高)
**预估时间**: 4-6小时
**负责人**: Bob (架构师) + Alex (工程师)

### 任务描述
在所有开发工作开始前，首先要明确定义本次切片所需的所有API接口的详细规格。这份规格就是前后端协作的"法律合同"，是后续所有开发、测试工作的唯一依据。

### 具体实施步骤

#### 3.0.1 设计API端点、方法与数据结构 (DTOs)
明确所有端点、HTTP方法、请求体和响应体的确切字段名与数据类型。

**文件管理API接口设计**：

```javascript
// 1. 获取学科文件结构
GET /api/v1/subjects/:id/files
Response: {
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "subject": {
      "id": 1,
      "name": "高等数学",
      "description": "高等数学复习资料"
    },
    "file_tree": [
      {
        "id": 1,
        "name": "第一章",
        "type": "folder",
        "parent_id": null,
        "relative_path": "第一章",
        "children": [
          {
            "id": 2,
            "name": "极限与连续.md",
            "type": "file",
            "parent_id": 1,
            "relative_path": "第一章/极限与连续.md",
            "file_size": 2048,
            "mime_type": "text/markdown",
            "created_at": "2025-01-08T10:00:00.000Z"
          }
        ]
      }
    ]
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 2. 获取文件内容
GET /api/v1/files/:id
Response: {
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": {
    "file": {
      "id": 2,
      "name": "极限与连续.md",
      "type": "file",
      "relative_path": "第一章/极限与连续.md",
      "file_size": 2048,
      "mime_type": "text/markdown",
      "created_at": "2025-01-08T10:00:00.000Z"
    },
    "content": "# 极限与连续\n\n## 1.1 极限的定义\n...",
    "subject": {
      "id": 1,
      "name": "高等数学"
    },
    "breadcrumb": [
      { "id": 1, "name": "高等数学", "type": "subject" },
      { "id": 1, "name": "第一章", "type": "folder" },
      { "id": 2, "name": "极限与连续.md", "type": "file" }
    ]
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 3. 获取静态资源文件
GET /api/v1/assets/:fileNodeId
Response: Binary data (image/png, image/jpeg, etc.)
Headers: {
  "Content-Type": "image/png",
  "Cache-Control": "public, max-age=31536000",
  "ETag": "\"abc123\"",
  "Last-Modified": "Wed, 08 Jan 2025 10:00:00 GMT"
}

// 4. 获取学科列表（访客版本，简化信息）
GET /api/v1/subjects/public
Response: {
  "success": true,
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "高等数学",
      "description": "高等数学复习资料",
      "file_count": 15,
      "total_size": 2048576,
      "last_updated": "2025-01-08T10:00:00.000Z",
      "cover_image": "/api/v1/assets/123"
    }
  ],
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 5. 搜索文件（可选功能）
GET /api/v1/search?q=极限&subject_id=1
Response: {
  "success": true,
  "code": 200,
  "message": "搜索完成",
  "data": {
    "query": "极限",
    "total": 3,
    "results": [
      {
        "id": 2,
        "name": "极限与连续.md",
        "type": "file",
        "subject_id": 1,
        "subject_name": "高等数学",
        "relative_path": "第一章/极限与连续.md",
        "highlight": "...关于<mark>极限</mark>的定义...",
        "relevance_score": 0.95
      }
    ]
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}
```

#### 3.0.2 确定状态码与统一错误格式
明确所有成功和失败场景下的HTTP状态码，并统一错误响应的JSON结构。

**错误响应格式**：
```javascript
// 文件不存在错误
{
  "success": false,
  "code": 404,
  "message": "文件不存在",
  "error": "FILE_NOT_FOUND",
  "details": {
    "file_id": 999,
    "requested_path": "/api/v1/files/999"
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 学科不存在错误
{
  "success": false,
  "code": 404,
  "message": "学科不存在",
  "error": "SUBJECT_NOT_FOUND",
  "details": {
    "subject_id": 999
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 文件内容读取错误
{
  "success": false,
  "code": 500,
  "message": "文件内容读取失败",
  "error": "FILE_READ_ERROR",
  "details": {
    "file_id": 2,
    "storage_path": "/uploads/subject1/chapter1/limits.md"
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}

// 静态资源不存在错误
{
  "success": false,
  "code": 404,
  "message": "资源文件不存在",
  "error": "ASSET_NOT_FOUND",
  "details": {
    "file_node_id": 123,
    "asset_path": "/uploads/images/diagram.png"
  },
  "timestamp": "2025-01-08T10:00:00.000Z"
}
```

#### 3.0.3 编写初始文档
在 `/docs/architecture/API_Reference.md` 中更新API契约文档。

#### 3.0.4 评审与冻结
由项目负责人最终评审并"冻结"此API契约。

### 验收标准
- [ ] 一份详细的、已评审通过的文件管理API契约文档已创建
- [ ] 所有相关人员都已理解并同意遵守此契约
- [ ] API契约文档已更新到 `/docs/architecture/API_Reference.md`
- [ ] 响应格式和错误处理标准已明确定义
- [ ] 文件树数据结构和面包屑导航数据格式已明确定义

### 相关文件
- `/docs/architecture/API_Reference.md` - API契约文档 (更新)
- `/docs/architecture/Backend_Architecture_and_Guide.md` - 后端架构指南 (参考)

---

## 任务3.1: 环境与数据模型准备 (Setup)

**优先级**: P0 (最高)
**预估时间**: 4-5小时
**负责人**: Alex (工程师)
**依赖任务**: 任务3.0 (API契约设计与评审)

### 任务描述
基于任务3.0中已确定的API契约，确保数据库表结构完全支持文件浏览功能，实现FileNode数据模型和文件服务层，支持文件树查询和内容读取。

### 具体实施步骤

#### 3.1.1 验证和优化数据库表结构
确保file_nodes表结构支持文件树查询和排序需求：

```sql
-- 验证现有file_nodes表结构
SELECT sql FROM sqlite_master WHERE type='table' AND name='file_nodes';

-- 添加必要的索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_parent ON file_nodes(subject_id, parent_id);
CREATE INDEX IF NOT EXISTS idx_file_nodes_type_name ON file_nodes(type, name);

-- 创建视图简化文件树查询
CREATE VIEW IF NOT EXISTS file_tree_view AS
SELECT 
    fn.id,
    fn.subject_id,
    fn.parent_id,
    fn.name,
    fn.type,
    fn.relative_path,
    fn.storage_url,
    fn.file_size,
    fn.mime_type,
    fn.created_at,
    fn.updated_at,
    s.name as subject_name
FROM file_nodes fn
LEFT JOIN subjects s ON fn.subject_id = s.id
ORDER BY fn.type DESC, fn.name ASC;
```

#### 3.1.2 实现FileNode数据模型类
```javascript
// backend/src/models/FileNode.js
class FileNode {
  constructor(db) {
    this.db = db;
  }

  // 获取学科的文件树结构
  getSubjectFileTree(subjectId) {
    const stmt = this.db.prepare(`
      SELECT id, subject_id, parent_id, name, type, relative_path,
             file_size, mime_type, created_at, updated_at
      FROM file_nodes
      WHERE subject_id = ?
      ORDER BY type DESC, name ASC
    `);

    const nodes = stmt.all(subjectId);
    return this.buildTree(nodes);
  }

  // 构建树形结构
  buildTree(nodes, parentId = null) {
    const children = nodes
      .filter(node => node.parent_id === parentId)
      .map(node => ({
        ...node,
        children: node.type === 'folder' ? this.buildTree(nodes, node.id) : []
      }));

    return children;
  }

  // 根据ID获取文件节点
  findById(id) {
    const stmt = this.db.prepare(`
      SELECT fn.*, s.name as subject_name
      FROM file_nodes fn
      LEFT JOIN subjects s ON fn.subject_id = s.id
      WHERE fn.id = ?
    `);
    return stmt.get(id);
  }

  // 获取文件的面包屑路径
  getBreadcrumb(fileId) {
    const file = this.findById(fileId);
    if (!file) return [];

    const breadcrumb = [];
    let currentNode = file;

    // 添加文件本身
    breadcrumb.unshift({
      id: currentNode.id,
      name: currentNode.name,
      type: currentNode.type
    });

    // 向上遍历父节点
    while (currentNode.parent_id) {
      const stmt = this.db.prepare('SELECT * FROM file_nodes WHERE id = ?');
      currentNode = stmt.get(currentNode.parent_id);
      if (currentNode) {
        breadcrumb.unshift({
          id: currentNode.id,
          name: currentNode.name,
          type: currentNode.type
        });
      }
    }

    // 添加学科信息
    breadcrumb.unshift({
      id: file.subject_id,
      name: file.subject_name,
      type: 'subject'
    });

    return breadcrumb;
  }

  // 读取文件内容
  readFileContent(fileId) {
    const file = this.findById(fileId);
    if (!file || file.type !== 'file') {
      return null;
    }

    const fs = require('fs');
    const path = require('path');

    try {
      const filePath = path.join(process.env.UPLOAD_PATH || './uploads', file.storage_url);
      const content = fs.readFileSync(filePath, 'utf8');
      return content;
    } catch (error) {
      console.error('Error reading file:', error);
      return null;
    }
  }

  // 获取静态资源文件路径
  getAssetPath(fileNodeId) {
    const file = this.findById(fileNodeId);
    if (!file) return null;

    const path = require('path');
    return path.join(process.env.UPLOAD_PATH || './uploads', file.storage_url);
  }
}

module.exports = FileNode;
```

### 验收标准
- [ ] 数据库表结构和索引优化完成，支持高效的文件树查询
- [ ] FileNode数据模型类实现完成，支持所有文件操作
- [ ] FileService服务层实现完成，支持业务逻辑处理
- [ ] 文件内容读取和静态资源访问功能正常
- [ ] 面包屑导航数据生成功能正常
- [ ] 更新后端指南文档 (`/docs/architecture/Backend_Architecture_and_Guide.md`)

### 相关文件
- `backend/src/models/FileNode.js` - 文件节点数据模型 (创建)
- `backend/src/services/fileService.js` - 文件业务逻辑服务 (创建)
- `backend/src/config/database.js` - 数据库配置 (更新)
- `/docs/architecture/Backend_Architecture_and_Guide.md` - 后端架构指南 (更新)

---

## 任务3.2: 后端API开发与测试闭环 (Backend Loop)

**优先级**: P0 (最高)
**预估时间**: 10-12小时
**负责人**: Alex (工程师)
**依赖任务**: 任务3.1 (环境与数据模型准备)

### 任务描述
严格按照任务3.0的API契约，实现所有文件管理相关的API接口。采用TDD开发模式，先写测试，再写实现，确保代码质量和业务逻辑正确性。

### 具体实施步骤

#### 3.2.1 创建文件控制器 (TDD方式)

**第一步：编写控制器测试**
```javascript
// tests/integration/fileController.test.js
const request = require('supertest');
const app = require('../../src/app');

describe('File Controller', () => {
  let testSubjectId;
  let testFileId;

  beforeEach(async () => {
    // 创建测试数据
    testSubjectId = await createTestSubject({ name: '测试学科' });
    testFileId = await createTestFile({
      subject_id: testSubjectId,
      name: 'test.md',
      type: 'file',
      content: '# 测试文件\n\n这是测试内容。'
    });
  });

  describe('GET /api/v1/subjects/:id/files', () => {
    test('should return subject file tree', async () => {
      const response = await request(app.callback())
        .get(`/api/v1/subjects/${testSubjectId}/files`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        code: 200,
        message: '获取成功'
      });

      expect(response.body.data.subject).toMatchObject({
        id: testSubjectId,
        name: '测试学科'
      });

      expect(Array.isArray(response.body.data.file_tree)).toBe(true);
    });

    test('should return 404 for non-existent subject', async () => {
      const response = await request(app.callback())
        .get('/api/v1/subjects/999/files')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        code: 404,
        message: '学科不存在',
        error: 'SUBJECT_NOT_FOUND'
      });
    });
  });

  describe('GET /api/v1/files/:id', () => {
    test('should return file content with breadcrumb', async () => {
      const response = await request(app.callback())
        .get(`/api/v1/files/${testFileId}`)
        .expect(200);

      expect(response.body.data.file).toMatchObject({
        id: testFileId,
        name: 'test.md',
        type: 'file'
      });

      expect(response.body.data.content).toContain('# 测试文件');
      expect(Array.isArray(response.body.data.breadcrumb)).toBe(true);
    });
  });
});
```

**第二步：实现文件控制器**
```javascript
// backend/src/controllers/fileController.js
const FileService = require('../services/fileService');
const FileNode = require('../models/FileNode');
const Subject = require('../models/Subject');

class FileController {
  constructor() {
    this.fileService = new FileService(
      new FileNode(global.db),
      new Subject(global.db)
    );
  }

  async getSubjectFileTree(ctx) {
    try {
      const { id } = ctx.params;
      const result = await this.fileService.getSubjectFileTree(parseInt(id));

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '获取成功',
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async getFileContent(ctx) {
    try {
      const { id } = ctx.params;
      const result = await this.fileService.getFileContent(parseInt(id));

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '获取成功',
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async getAssetFile(ctx) {
    try {
      const { fileNodeId } = ctx.params;
      const result = await this.fileService.getAssetFile(parseInt(fileNodeId));

      const fs = require('fs');
      const mime = require('mime-types');

      // 设置响应头
      const mimeType = mime.lookup(result.path) || 'application/octet-stream';
      ctx.type = mimeType;
      ctx.set('Cache-Control', 'public, max-age=31536000');
      ctx.set('ETag', `"${result.stats.mtime.getTime()}"`);

      // 返回文件内容
      ctx.body = fs.createReadStream(result.path);
    } catch (error) {
      throw error;
    }
  }

  async getPublicSubjects(ctx) {
    try {
      const subjects = await this.fileService.getPublicSubjects();

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '获取成功',
        data: subjects,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }
}

module.exports = FileController;
```

#### 3.2.2 配置路由
```javascript
// backend/src/routes/files.js
const Router = require('koa-router');
const FileController = require('../controllers/fileController');

const router = new Router({ prefix: '/api/v1' });
const fileController = new FileController();

// 获取学科文件结构
router.get('/subjects/:id/files', fileController.getSubjectFileTree.bind(fileController));

// 获取文件内容
router.get('/files/:id', fileController.getFileContent.bind(fileController));

// 获取静态资源
router.get('/assets/:fileNodeId', fileController.getAssetFile.bind(fileController));

// 获取公开学科列表
router.get('/subjects/public', fileController.getPublicSubjects.bind(fileController));

module.exports = router;
```

### 验收标准
- [ ] 所有文件管理API接口按照契约实现完成
- [ ] 编写测试脚本进行测试，测试覆盖率 > 90%
- [ ] 确保API行为与契约100%一致
- [ ] 文件树查询性能优化，响应时间 < 1秒
- [ ] 静态资源服务支持缓存和ETag
- [ ] 任何API的变更都需要更新API参考文档
- [ ] 错误处理和状态码符合契约规范

### 相关文件
- `backend/src/controllers/fileController.js` - 文件管理控制器 (创建)
- `backend/src/routes/files.js` - 文件路由配置 (创建)
- `tests/integration/fileController.test.js` - 文件控制器集成测试 (创建)
- `/docs/architecture/API_Reference.md` - API参考文档 (更新)

---

## 任务3.3: 前端UI开发与测试闭环 (Frontend Loop)

**优先级**: P0 (最高)
**预估时间**: 12-14小时
**负责人**: Alex (工程师)
**依赖任务**: 任务3.2 (后端API开发与测试闭环)

### 任务描述
严格基于任务3.0的API契约，进行前端开发。实现访客浏览的完整用户界面，包括首页、学科详情、文件查看、文件树组件、Markdown渲染等，确保用户体验优秀。

### 具体实施步骤

#### 3.3.1 更新API服务层
```typescript
// frontend/src/services/fileApi.ts
import api from './api'

export interface FileNode {
  id: number
  name: string
  type: 'file' | 'folder'
  parent_id?: number
  relative_path: string
  file_size?: number
  mime_type?: string
  created_at: string
  children?: FileNode[]
}

export interface FileTreeResponse {
  success: boolean
  code: number
  message: string
  data: {
    subject: {
      id: number
      name: string
      description?: string
    }
    file_tree: FileNode[]
  }
  timestamp: string
}

export interface FileContentResponse {
  success: boolean
  code: number
  message: string
  data: {
    file: {
      id: number
      name: string
      type: string
      relative_path: string
      file_size: number
      mime_type: string
      created_at: string
    }
    content: string
    subject: {
      id: number
      name: string
    }
    breadcrumb: Array<{
      id: number
      name: string
      type: 'subject' | 'folder' | 'file'
    }>
  }
  timestamp: string
}

export interface PublicSubject {
  id: number
  name: string
  description?: string
  file_count: number
  total_size: number
  last_updated: string
  cover_image?: string
}

export const fileApi = {
  // 获取学科文件结构
  getSubjectFiles: (subjectId: number): Promise<FileTreeResponse> =>
    api.get(`/subjects/${subjectId}/files`),

  // 获取文件内容
  getFileContent: (fileId: number): Promise<FileContentResponse> =>
    api.get(`/files/${fileId}`),

  // 获取公开学科列表
  getPublicSubjects: (): Promise<{ success: boolean; data: PublicSubject[] }> =>
    api.get('/subjects/public'),

  // 获取静态资源URL
  getAssetUrl: (fileNodeId: number): string =>
    `/api/v1/assets/${fileNodeId}`
}
```

#### 3.3.2 实现Markdown渲染组件
```vue
<!-- frontend/src/components/MarkdownRenderer.vue -->
<template>
  <div class="markdown-renderer">
    <div
      class="markdown-content"
      v-html="renderedContent"
      @click="handleImageClick"
    />

    <!-- 图片预览模态框 -->
    <a-modal
      v-model:visible="imagePreviewVisible"
      :footer="null"
      :width="800"
      centered
    >
      <img
        :src="previewImageSrc"
        :alt="previewImageAlt"
        style="width: 100%; height: auto;"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

interface Props {
  content: string
  baseUrl?: string
}

const props = withDefaults(defineProps<Props>(), {
  baseUrl: ''
})

const imagePreviewVisible = ref(false)
const previewImageSrc = ref('')
const previewImageAlt = ref('')

// 配置marked
const renderer = new marked.Renderer()

// 自定义图片渲染
renderer.image = (href: string, title: string | null, text: string) => {
  const src = href.startsWith('http') ? href : `${props.baseUrl}${href}`
  return `<img src="${src}" alt="${text}" title="${title || text}" class="markdown-image" loading="lazy" />`
}

// 自定义代码块渲染
renderer.code = (code: string, language: string | undefined) => {
  const validLanguage = language && hljs.getLanguage(language) ? language : 'plaintext'
  const highlighted = hljs.highlight(code, { language: validLanguage }).value
  return `<pre><code class="hljs language-${validLanguage}">${highlighted}</code></pre>`
}

// 自定义链接渲染
renderer.link = (href: string, title: string | null, text: string) => {
  const isExternal = href.startsWith('http')
  const target = isExternal ? '_blank' : '_self'
  const rel = isExternal ? 'noopener noreferrer' : ''
  return `<a href="${href}" title="${title || text}" target="${target}" rel="${rel}">${text}</a>`
}

marked.setOptions({
  renderer,
  gfm: true,
  breaks: true,
  pedantic: false,
  sanitize: false,
  smartLists: true,
  smartypants: false
})

const renderedContent = computed(() => {
  try {
    return marked(props.content || '')
  } catch (error) {
    console.error('Markdown rendering error:', error)
    return '<p>内容渲染失败</p>'
  }
})

const handleImageClick = (event: Event) => {
  const target = event.target as HTMLElement
  if (target.tagName === 'IMG') {
    const img = target as HTMLImageElement
    previewImageSrc.value = img.src
    previewImageAlt.value = img.alt
    imagePreviewVisible.value = true
  }
}

onMounted(() => {
  // 处理图片加载错误
  const images = document.querySelectorAll('.markdown-content img')
  images.forEach(img => {
    img.addEventListener('error', (e) => {
      const target = e.target as HTMLImageElement
      target.src = '/placeholder-image.png'
      target.alt = '图片加载失败'
    })
  })
})
</script>

<style scoped>
.markdown-renderer {
  width: 100%;
}

.markdown-content {
  line-height: 1.6;
  color: #333;
  font-size: 16px;
}

.markdown-content :deep(h1) {
  font-size: 2em;
  font-weight: 600;
  margin: 1em 0 0.5em 0;
  padding-bottom: 0.3em;
  border-bottom: 1px solid #eaecef;
}

.markdown-content :deep(h2) {
  font-size: 1.5em;
  font-weight: 600;
  margin: 1em 0 0.5em 0;
  padding-bottom: 0.3em;
  border-bottom: 1px solid #eaecef;
}

.markdown-content :deep(h3) {
  font-size: 1.25em;
  font-weight: 600;
  margin: 1em 0 0.5em 0;
}

.markdown-content :deep(p) {
  margin: 1em 0;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin: 1em 0;
  padding-left: 2em;
}

.markdown-content :deep(blockquote) {
  margin: 1em 0;
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
}

.markdown-content :deep(table) {
  border-collapse: collapse;
  margin: 1em 0;
  width: 100%;
}

.markdown-content :deep(table th),
.markdown-content :deep(table td) {
  border: 1px solid #dfe2e5;
  padding: 6px 13px;
}

.markdown-content :deep(table th) {
  background-color: #f6f8fa;
  font-weight: 600;
}

.markdown-content :deep(.markdown-image) {
  max-width: 100%;
  height: auto;
  cursor: pointer;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.markdown-content :deep(.markdown-image:hover) {
  transform: scale(1.02);
}

.markdown-content :deep(pre) {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  margin: 1em 0;
}

.markdown-content :deep(code) {
  background-color: #f6f8fa;
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-size: 85%;
}

.markdown-content :deep(pre code) {
  background-color: transparent;
  padding: 0;
}

@media (max-width: 768px) {
  .markdown-content {
    font-size: 14px;
  }

  .markdown-content :deep(h1) {
    font-size: 1.8em;
  }

  .markdown-content :deep(h2) {
    font-size: 1.4em;
  }

  .markdown-content :deep(table) {
    font-size: 12px;
  }

  .markdown-content :deep(pre) {
    padding: 12px;
  }
}
</style>
```

#### 3.3.3 实现文件树组件
```vue
<!-- frontend/src/components/FileTree.vue -->
<template>
  <div class="file-tree">
    <a-tree
      :tree-data="treeData"
      :show-icon="true"
      :selectable="true"
      :block-node="true"
      @select="handleSelect"
      :selected-keys="selectedKeys"
    >
      <template #icon="{ type, expanded }">
        <FolderOpenOutlined v-if="type === 'folder' && expanded" />
        <FolderOutlined v-else-if="type === 'folder'" />
        <FileTextOutlined v-else-if="type === 'file'" />
      </template>

      <template #title="{ name, type, file_size }">
        <div class="tree-node-title">
          <span class="node-name">{{ name }}</span>
          <span v-if="type === 'file' && file_size" class="file-size">
            {{ formatFileSize(file_size) }}
          </span>
        </div>
      </template>
    </a-tree>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  FolderOutlined,
  FolderOpenOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import type { FileNode } from '@/services/fileApi'

interface Props {
  fileTree: FileNode[]
  selectedFileId?: number
}

interface Emits {
  (e: 'select', fileNode: FileNode): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const selectedKeys = ref<string[]>([])

// 转换文件树数据为Ant Design Tree组件格式
const treeData = computed(() => {
  return convertToTreeData(props.fileTree)
})

function convertToTreeData(nodes: FileNode[]): any[] {
  return nodes.map(node => ({
    key: node.id.toString(),
    title: node.name,
    type: node.type,
    file_size: node.file_size,
    children: node.children ? convertToTreeData(node.children) : [],
    isLeaf: node.type === 'file',
    selectable: node.type === 'file',
    _original: node
  }))
}

const handleSelect = (selectedKeysValue: string[], info: any) => {
  if (info.selected && info.node._original) {
    selectedKeys.value = selectedKeysValue
    emit('select', info.node._original)
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 监听选中的文件ID变化
watch(() => props.selectedFileId, (newId) => {
  if (newId) {
    selectedKeys.value = [newId.toString()]
  }
}, { immediate: true })
</script>

<style scoped>
.file-tree {
  background: white;
  border-radius: 6px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tree-node-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.node-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}

:deep(.ant-tree-node-content-wrapper) {
  width: 100%;
}

:deep(.ant-tree-title) {
  width: 100%;
}

@media (max-width: 768px) {
  .file-tree {
    padding: 12px;
  }

  .file-size {
    display: none;
  }
}
</style>
```

#### 3.3.4 实现访客首页
```vue
<!-- frontend/src/pages/HomePage.vue -->
<template>
  <div class="home-page">
    <a-layout>
      <a-layout-header class="header">
        <div class="header-content">
          <h1>期末复习平台</h1>
          <p class="subtitle">免费的在线学习资料库</p>
        </div>
      </a-layout-header>

      <a-layout-content class="content">
        <div class="subjects-container">
          <a-spin :spinning="loading" tip="加载中...">
            <div v-if="subjects.length === 0 && !loading" class="empty-state">
              <a-empty description="暂无学科资料">
                <p>管理员还没有上传任何学科资料</p>
              </a-empty>
            </div>

            <div v-else class="subjects-grid">
              <a-card
                v-for="subject in subjects"
                :key="subject.id"
                class="subject-card"
                :hoverable="true"
                @click="goToSubject(subject.id)"
              >
                <template #cover>
                  <div class="card-cover">
                    <BookOutlined class="cover-icon" />
                  </div>
                </template>

                <a-card-meta>
                  <template #title>
                    <div class="subject-title">{{ subject.name }}</div>
                  </template>

                  <template #description>
                    <div class="subject-description">
                      {{ subject.description || '暂无描述' }}
                    </div>

                    <div class="subject-stats">
                      <a-space>
                        <span>
                          <FileOutlined />
                          {{ subject.file_count }} 个文件
                        </span>
                        <span>
                          <DatabaseOutlined />
                          {{ formatFileSize(subject.total_size) }}
                        </span>
                      </a-space>
                    </div>

                    <div class="last-updated">
                      最后更新: {{ formatDate(subject.last_updated) }}
                    </div>
                  </template>
                </a-card-meta>
              </a-card>
            </div>
          </a-spin>
        </div>
      </a-layout-content>

      <a-layout-footer class="footer">
        <p>&copy; 2025 期末复习平台. 所有内容仅供学习使用.</p>
      </a-layout-footer>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  BookOutlined,
  FileOutlined,
  DatabaseOutlined
} from '@ant-design/icons-vue'
import { fileApi, type PublicSubject } from '@/services/fileApi'

const router = useRouter()
const subjects = ref<PublicSubject[]>([])
const loading = ref(false)

const loadSubjects = async () => {
  try {
    loading.value = true
    const response = await fileApi.getPublicSubjects()
    subjects.value = response.data
  } catch (error: any) {
    message.error(error.response?.data?.message || '加载学科列表失败')
  } finally {
    loading.value = false
  }
}

const goToSubject = (subjectId: number) => {
  router.push(`/subjects/${subjectId}`)
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

onMounted(() => {
  loadSubjects()
})
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 24px;
}

.header-content {
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 0;
}

.header-content h1 {
  margin: 0;
  color: #1890ff;
  font-size: 32px;
  font-weight: 700;
}

.subtitle {
  margin: 8px 0 0 0;
  color: #666;
  font-size: 16px;
}

.content {
  padding: 40px 24px;
  background: transparent;
}

.subjects-container {
  max-width: 1200px;
  margin: 0 auto;
}

.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.subject-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
}

.subject-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-cover {
  height: 120px;
  background: linear-gradient(45deg, #1890ff, #36cfc9);
  display: flex;
  align-items: center;
  justify-content: center;
}

.cover-icon {
  font-size: 48px;
  color: white;
}

.subject-title {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8px;
}

.subject-description {
  color: #666;
  margin-bottom: 12px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.subject-stats {
  margin-bottom: 8px;
  color: #999;
  font-size: 14px;
}

.last-updated {
  color: #ccc;
  font-size: 12px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
}

.footer {
  background: rgba(255, 255, 255, 0.95);
  text-align: center;
  color: #666;
}

@media (max-width: 768px) {
  .subjects-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .header-content h1 {
    font-size: 24px;
  }

  .content {
    padding: 24px 16px;
  }
}
</style>
```

### 验收标准
- [ ] 前端项目能够正常启动和运行
- [ ] 访客首页实现完成，展示学科卡片列表
- [ ] Markdown渲染组件实现完成，支持代码高亮和图片预览
- [ ] 文件树组件实现完成，支持层级展示和文件选择
- [ ] API服务层实现完成，与后端API契约完全对应
- [ ] 响应式设计，适配多种设备
- [ ] 组件测试覆盖率 > 80%
- [ ] 前端开发指南文档已更新 (`/docs/development/Frontend_Development_Guide.md`)

### 相关文件
- `frontend/src/services/fileApi.ts` - 文件API服务 (创建)
- `frontend/src/components/MarkdownRenderer.vue` - Markdown渲染组件 (创建)
- `frontend/src/components/FileTree.vue` - 文件树组件 (创建)
- `frontend/src/pages/HomePage.vue` - 访客首页 (创建)
- `/docs/development/Frontend_Development_Guide.md` - 前端开发指南 (更新)

---

## 任务3.4: 系统集成与端到端测试闭环 (E2E Loop)

**优先级**: P0 (最高)
**预估时间**: 8-10小时
**负责人**: Alex (工程师)
**依赖任务**: 任务3.3 (前端UI开发与测试闭环)

### 任务描述
进行前后端数据联调，验证连接通畅性。使用Playwright MCP进行端到端测试，模拟真实用户的访客浏览操作流程。

### 具体实施步骤

#### 3.4.1 集成联调
进行前后端数据联调，验证连接通畅性。

**更新代理配置**：
```typescript
// frontend/vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'

export default defineConfig({
  plugins: [vue(), UnoCSS()],
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true
      }
    }
  },
  resolve: {
    alias: {
      '@': '/src'
    }
  }
})
```

#### 3.4.2 端到端测试 (使用Playwright MCP)
使用Playwright MCP，模拟真实用户的操作流程。

**访客浏览E2E测试**：
```javascript
// tests/e2e/visitor-browsing.spec.js
const { test, expect } = require('@playwright/test');

test.describe('访客浏览功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问首页
    await page.goto('http://localhost:5173');

    // 等待页面加载完成
    await page.waitForSelector('.home-page', { timeout: 10000 });
  });

  test('访客可以查看首页学科列表', async ({ page }) => {
    // 验证页面标题
    await expect(page).toHaveTitle(/期末复习平台/);

    // 验证页面标题和副标题
    await expect(page.locator('h1')).toContainText('期末复习平台');
    await expect(page.locator('.subtitle')).toContainText('免费的在线学习资料库');

    // 验证学科卡片存在
    await expect(page.locator('.subject-card')).toBeVisible();

    // 验证学科信息显示
    const firstCard = page.locator('.subject-card').first();
    await expect(firstCard.locator('.subject-title')).toBeVisible();
    await expect(firstCard.locator('.subject-stats')).toBeVisible();
  });

  test('访客可以点击学科进入详情页', async ({ page }) => {
    // 点击第一个学科卡片
    await page.click('.subject-card:first-child');

    // 验证跳转到学科详情页
    await expect(page).toHaveURL(/\/subjects\/\d+/);

    // 验证学科详情页加载
    await page.waitForSelector('.subject-detail', { timeout: 10000 });

    // 验证文件树组件存在
    await expect(page.locator('.file-tree')).toBeVisible();

    // 验证面包屑导航存在
    await expect(page.locator('.breadcrumb')).toBeVisible();
  });

  test('访客可以浏览文件树结构', async ({ page }) => {
    // 进入学科详情页
    await page.click('.subject-card:first-child');
    await page.waitForSelector('.file-tree');

    // 验证文件树节点存在
    await expect(page.locator('.ant-tree-node')).toBeVisible();

    // 展开文件夹（如果存在）
    const folderNode = page.locator('.ant-tree-node').filter({ hasText: /.*/ }).first();
    if (await folderNode.locator('.ant-tree-switcher').isVisible()) {
      await folderNode.locator('.ant-tree-switcher').click();

      // 验证子节点展开
      await expect(page.locator('.ant-tree-child-tree')).toBeVisible();
    }

    // 点击文件节点
    const fileNode = page.locator('.ant-tree-node').filter({ hasText: /\.md$/ }).first();
    if (await fileNode.isVisible()) {
      await fileNode.click();

      // 验证文件内容加载
      await page.waitForSelector('.file-content', { timeout: 5000 });
    }
  });

  test('访客可以查看Markdown文件内容', async ({ page }) => {
    // 进入学科详情页并选择文件
    await page.click('.subject-card:first-child');
    await page.waitForSelector('.file-tree');

    // 点击Markdown文件
    const markdownFile = page.locator('.ant-tree-node').filter({ hasText: /\.md$/ }).first();
    await markdownFile.click();

    // 验证文件查看页面
    await expect(page).toHaveURL(/\/files\/\d+/);

    // 验证Markdown内容渲染
    await expect(page.locator('.markdown-content')).toBeVisible();

    // 验证面包屑导航
    await expect(page.locator('.breadcrumb')).toBeVisible();

    // 验证返回按钮
    await expect(page.locator('.back-button')).toBeVisible();

    // 测试返回功能
    await page.click('.back-button');
    await expect(page).toHaveURL(/\/subjects\/\d+/);
  });

  test('Markdown渲染功能正常工作', async ({ page }) => {
    // 导航到包含Markdown内容的文件
    await page.click('.subject-card:first-child');
    await page.waitForSelector('.file-tree');

    const markdownFile = page.locator('.ant-tree-node').filter({ hasText: /\.md$/ }).first();
    await markdownFile.click();

    await page.waitForSelector('.markdown-content');

    // 验证标题渲染
    const h1Elements = page.locator('.markdown-content h1');
    if (await h1Elements.count() > 0) {
      await expect(h1Elements.first()).toBeVisible();
    }

    // 验证段落渲染
    const paragraphs = page.locator('.markdown-content p');
    if (await paragraphs.count() > 0) {
      await expect(paragraphs.first()).toBeVisible();
    }

    // 验证代码块渲染（如果存在）
    const codeBlocks = page.locator('.markdown-content pre code');
    if (await codeBlocks.count() > 0) {
      await expect(codeBlocks.first()).toBeVisible();
      await expect(codeBlocks.first()).toHaveClass(/hljs/);
    }

    // 验证图片渲染（如果存在）
    const images = page.locator('.markdown-content img');
    if (await images.count() > 0) {
      await expect(images.first()).toBeVisible();

      // 测试图片点击预览
      await images.first().click();
      await expect(page.locator('.ant-modal')).toBeVisible();

      // 关闭预览
      await page.keyboard.press('Escape');
      await expect(page.locator('.ant-modal')).not.toBeVisible();
    }
  });

  test('响应式设计在移动端正常工作', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 });

    // 验证首页在移动端正常显示
    await expect(page.locator('.home-page')).toBeVisible();
    await expect(page.locator('.subjects-grid')).toBeVisible();

    // 验证学科卡片在移动端的布局
    const cards = page.locator('.subject-card');
    const cardCount = await cards.count();

    if (cardCount > 0) {
      // 验证卡片垂直排列（移动端单列布局）
      const firstCard = cards.first();
      const secondCard = cards.nth(1);

      if (await secondCard.isVisible()) {
        const firstCardBox = await firstCard.boundingBox();
        const secondCardBox = await secondCard.boundingBox();

        // 第二个卡片应该在第一个卡片下方
        expect(secondCardBox.y).toBeGreaterThan(firstCardBox.y + firstCardBox.height);
      }
    }

    // 测试学科详情页在移动端的表现
    await page.click('.subject-card:first-child');
    await page.waitForSelector('.subject-detail');

    // 验证文件树在移动端的显示
    await expect(page.locator('.file-tree')).toBeVisible();

    // 验证文件内容在移动端的显示
    const markdownFile = page.locator('.ant-tree-node').filter({ hasText: /\.md$/ }).first();
    if (await markdownFile.isVisible()) {
      await markdownFile.click();
      await page.waitForSelector('.markdown-content');

      // 验证Markdown内容在移动端的响应式显示
      await expect(page.locator('.markdown-content')).toBeVisible();
    }
  });

  test('页面加载性能符合要求', async ({ page }) => {
    const startTime = Date.now();

    await page.goto('http://localhost:5173');
    await page.waitForSelector('.subjects-grid');

    const loadTime = Date.now() - startTime;

    // 页面加载时间应小于3秒
    expect(loadTime).toBeLessThan(3000);

    // 测试学科详情页加载性能
    const detailStartTime = Date.now();

    await page.click('.subject-card:first-child');
    await page.waitForSelector('.file-tree');

    const detailLoadTime = Date.now() - detailStartTime;

    // 学科详情页加载时间应小于3秒
    expect(detailLoadTime).toBeLessThan(3000);
  });

  test('错误处理正常工作', async ({ page }) => {
    // 测试访问不存在的学科
    await page.goto('http://localhost:5173/subjects/999999');

    // 验证404页面或错误提示
    await expect(page.locator('text=学科不存在')).toBeVisible();

    // 测试访问不存在的文件
    await page.goto('http://localhost:5173/files/999999');

    // 验证404页面或错误提示
    await expect(page.locator('text=文件不存在')).toBeVisible();
  });
});

test.describe('API集成测试', () => {
  test('文件管理API端点正常工作', async ({ request }) => {
    // 测试获取公开学科列表
    const subjectsResponse = await request.get('http://localhost:3000/api/v1/subjects/public');
    expect(subjectsResponse.ok()).toBeTruthy();

    const subjectsData = await subjectsResponse.json();
    expect(subjectsData.success).toBe(true);
    expect(Array.isArray(subjectsData.data)).toBe(true);

    if (subjectsData.data.length > 0) {
      const subjectId = subjectsData.data[0].id;

      // 测试获取学科文件结构
      const filesResponse = await request.get(`http://localhost:3000/api/v1/subjects/${subjectId}/files`);
      expect(filesResponse.ok()).toBeTruthy();

      const filesData = await filesResponse.json();
      expect(filesData.success).toBe(true);
      expect(filesData.data.subject).toBeDefined();
      expect(Array.isArray(filesData.data.file_tree)).toBe(true);
    }
  });
});
```

#### 3.4.3 修复与验证
修复在完整流程中发现的任何Bug。

### 验收标准
- [ ] 前后端联调成功，数据传输正常
- [ ] 用户可以顺畅地完成整个浏览流程
- [ ] 端到端测试100%通过
- [ ] 所有API接口响应时间 < 3秒
- [ ] 页面加载时间 < 3秒
- [ ] 支持100个并发访客浏览
- [ ] 响应式设计在各种设备上正常工作
- [ ] Markdown渲染功能完整，支持代码高亮和图片预览
- [ ] 错误处理机制完善，用户体验良好
- [ ] 更新用户交互清单文档 (`docs/prd/sprint-plans/用户交互清单_Sprint3.md`)
- [ ] 更新变更日志 (`/docs/CHANGELOG.md`)

### 相关文件
- `tests/e2e/visitor-browsing.spec.js` - 访客浏览E2E测试 (创建)
- `frontend/vite.config.ts` - Vite配置 (更新)
- `docs/prd/sprint-plans/用户交互清单_Sprint3.md` - 用户交互清单 (创建)
- `/docs/CHANGELOG.md` - 变更日志 (更新)

---

## 质量保证与测试策略

### 测试覆盖率要求
- **单元测试覆盖率**: > 90%
- **集成测试覆盖率**: > 85%
- **E2E测试覆盖率**: 100% (核心访客浏览流程)

### 性能要求
- **API响应时间**: < 3秒
- **页面加载时间**: < 3秒
- **文件树查询时间**: < 1秒
- **Markdown渲染时间**: < 2秒
- **并发用户支持**: 100个访客同时浏览

### 用户体验标准
- **导航清晰**: 面包屑导航、返回按钮
- **加载反馈**: 加载动画、骨架屏
- **错误处理**: 友好的404页面、错误提示
- **响应式设计**: 适配桌面、平板、手机

---

## 风险管控

| 风险 | 影响 | 概率 | 缓解措施 |
|------|------|------|----------|
| 大文件树加载性能问题 | 高 | 中 | 懒加载、虚拟滚动、分页加载 |
| Markdown渲染性能问题 | 中 | 低 | 内容缓存、异步渲染 |
| 图片加载失败 | 中 | 中 | 占位符、重试机制、CDN |
| 移动端体验问题 | 中 | 中 | 响应式测试、触摸优化 |

---

## 交付清单

### 代码交付物
- [ ] 后端文件管理API服务 (完整可运行)
- [ ] 前端访客浏览界面 (完整可运行)
- [ ] Markdown渲染组件 (支持代码高亮和图片预览)
- [ ] 文件树组件 (支持层级展示和文件选择)
- [ ] 测试用例 (单元测试 + 集成测试 + E2E测试)

### 功能交付物
- [ ] 访客首页 (学科列表展示)
- [ ] 学科详情页 (文件树浏览)
- [ ] 文件查看页 (Markdown内容渲染)
- [ ] 面包屑导航和返回功能
- [ ] 响应式用户界面
- [ ] 静态资源服务 (图片等)

### 文档交付物
- [ ] API契约文档 (更新)
- [ ] 后端架构指南 (更新)
- [ ] 前端开发指南 (更新)
- [ ] 用户交互清单 (新建)
- [ ] 变更日志 (更新)

---

**文档结束**

*本Sprint-3详细任务计划将确保访客浏览核心体验的高质量实现，为用户提供流畅的学习资料浏览体验。*
```
```

#### 3.1.2 实现FileNode数据模型类
```javascript
// backend/src/models/FileNode.js
class FileNode {
  constructor(db) {
    this.db = db;
  }

  // 获取学科的文件树结构
  getSubjectFileTree(subjectId) {
    const stmt = this.db.prepare(`
      SELECT id, subject_id, parent_id, name, type, relative_path,
             file_size, mime_type, created_at, updated_at
      FROM file_nodes
      WHERE subject_id = ?
      ORDER BY type DESC, name ASC
    `);

    const nodes = stmt.all(subjectId);
    return this.buildTree(nodes);
  }

  // 构建树形结构
  buildTree(nodes, parentId = null) {
    const children = nodes
      .filter(node => node.parent_id === parentId)
      .map(node => ({
        ...node,
        children: node.type === 'folder' ? this.buildTree(nodes, node.id) : []
      }));

    return children;
  }

  // 根据ID获取文件节点
  findById(id) {
    const stmt = this.db.prepare(`
      SELECT fn.*, s.name as subject_name
      FROM file_nodes fn
      LEFT JOIN subjects s ON fn.subject_id = s.id
      WHERE fn.id = ?
    `);
    return stmt.get(id);
  }

  // 获取文件的面包屑路径
  getBreadcrumb(fileId) {
    const file = this.findById(fileId);
    if (!file) return [];

    const breadcrumb = [];
    let currentNode = file;

    // 添加文件本身
    breadcrumb.unshift({
      id: currentNode.id,
      name: currentNode.name,
      type: currentNode.type
    });

    // 向上遍历父节点
    while (currentNode.parent_id) {
      const stmt = this.db.prepare('SELECT * FROM file_nodes WHERE id = ?');
      currentNode = stmt.get(currentNode.parent_id);
      if (currentNode) {
        breadcrumb.unshift({
          id: currentNode.id,
          name: currentNode.name,
          type: currentNode.type
        });
      }
    }

    // 添加学科信息
    breadcrumb.unshift({
      id: file.subject_id,
      name: file.subject_name,
      type: 'subject'
    });

    return breadcrumb;
  }

  // 读取文件内容
  readFileContent(fileId) {
    const file = this.findById(fileId);
    if (!file || file.type !== 'file') {
      return null;
    }

    const fs = require('fs');
    const path = require('path');

    try {
      const filePath = path.join(process.env.UPLOAD_PATH || './uploads', file.storage_url);
      const content = fs.readFileSync(filePath, 'utf8');
      return content;
    } catch (error) {
      console.error('Error reading file:', error);
      return null;
    }
  }

  // 获取静态资源文件路径
  getAssetPath(fileNodeId) {
    const file = this.findById(fileNodeId);
    if (!file) return null;

    const path = require('path');
    return path.join(process.env.UPLOAD_PATH || './uploads', file.storage_url);
  }

  // 搜索文件（可选功能）
  searchFiles(query, subjectId = null) {
    let sql = `
      SELECT fn.*, s.name as subject_name
      FROM file_nodes fn
      LEFT JOIN subjects s ON fn.subject_id = s.id
      WHERE fn.type = 'file' AND fn.name LIKE ?
    `;

    const params = [`%${query}%`];

    if (subjectId) {
      sql += ' AND fn.subject_id = ?';
      params.push(subjectId);
    }

    sql += ' ORDER BY fn.name ASC LIMIT 50';

    const stmt = this.db.prepare(sql);
    return stmt.all(...params);
  }
}

module.exports = FileNode;
```

#### 3.1.3 创建文件服务层
```javascript
// backend/src/services/fileService.js
const FileNode = require('../models/FileNode');
const Subject = require('../models/Subject');

class FileService {
  constructor(fileNodeModel, subjectModel) {
    this.FileNode = fileNodeModel;
    this.Subject = subjectModel;
  }

  async getSubjectFileTree(subjectId) {
    // 验证学科是否存在
    const subject = this.Subject.findById(subjectId);
    if (!subject) {
      const error = new Error('学科不存在');
      error.code = 'SUBJECT_NOT_FOUND';
      error.status = 404;
      throw error;
    }

    // 获取文件树
    const fileTree = this.FileNode.getSubjectFileTree(subjectId);

    return {
      subject: {
        id: subject.id,
        name: subject.name,
        description: subject.description
      },
      file_tree: fileTree
    };
  }

  async getFileContent(fileId) {
    // 获取文件信息
    const file = this.FileNode.findById(fileId);
    if (!file) {
      const error = new Error('文件不存在');
      error.code = 'FILE_NOT_FOUND';
      error.status = 404;
      throw error;
    }

    if (file.type !== 'file') {
      const error = new Error('请求的不是文件');
      error.code = 'NOT_A_FILE';
      error.status = 400;
      throw error;
    }

    // 读取文件内容
    const content = this.FileNode.readFileContent(fileId);
    if (content === null) {
      const error = new Error('文件内容读取失败');
      error.code = 'FILE_READ_ERROR';
      error.status = 500;
      throw error;
    }

    // 获取面包屑导航
    const breadcrumb = this.FileNode.getBreadcrumb(fileId);

    return {
      file: {
        id: file.id,
        name: file.name,
        type: file.type,
        relative_path: file.relative_path,
        file_size: file.file_size,
        mime_type: file.mime_type,
        created_at: file.created_at
      },
      content: content,
      subject: {
        id: file.subject_id,
        name: file.subject_name
      },
      breadcrumb: breadcrumb
    };
  }

  async getAssetFile(fileNodeId) {
    const assetPath = this.FileNode.getAssetPath(fileNodeId);
    if (!assetPath) {
      const error = new Error('资源文件不存在');
      error.code = 'ASSET_NOT_FOUND';
      error.status = 404;
      throw error;
    }

    const fs = require('fs');

    try {
      // 检查文件是否存在
      if (!fs.existsSync(assetPath)) {
        const error = new Error('资源文件不存在');
        error.code = 'ASSET_NOT_FOUND';
        error.status = 404;
        throw error;
      }

      return {
        path: assetPath,
        stats: fs.statSync(assetPath)
      };
    } catch (error) {
      if (error.code === 'ASSET_NOT_FOUND') {
        throw error;
      }

      const newError = new Error('资源文件访问失败');
      newError.code = 'ASSET_ACCESS_ERROR';
      newError.status = 500;
      throw newError;
    }
  }

  async getPublicSubjects() {
    const subjects = this.Subject.findAll();

    // 为访客用户简化学科信息
    return subjects.map(subject => ({
      id: subject.id,
      name: subject.name,
      description: subject.description,
      file_count: subject.file_count || 0,
      total_size: subject.total_size || 0,
      last_updated: subject.updated_at,
      cover_image: null // 可以后续添加封面图片功能
    }));
  }

  async searchFiles(query, subjectId = null) {
    if (!query || query.trim().length < 2) {
      const error = new Error('搜索关键词至少需要2个字符');
      error.code = 'INVALID_SEARCH_QUERY';
      error.status = 400;
      throw error;
    }

    const results = this.FileNode.searchFiles(query.trim(), subjectId);

    return {
      query: query.trim(),
      total: results.length,
      results: results.map(file => ({
        id: file.id,
        name: file.name,
        type: file.type,
        subject_id: file.subject_id,
        subject_name: file.subject_name,
        relative_path: file.relative_path,
        highlight: this.generateHighlight(file.name, query),
        relevance_score: this.calculateRelevance(file.name, query)
      }))
    };
  }

  generateHighlight(text, query) {
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  calculateRelevance(text, query) {
    const lowerText = text.toLowerCase();
    const lowerQuery = query.toLowerCase();

    if (lowerText === lowerQuery) return 1.0;
    if (lowerText.startsWith(lowerQuery)) return 0.9;
    if (lowerText.includes(lowerQuery)) return 0.7;
    return 0.5;
  }
}

module.exports = FileService;
```

### 验收标准
- [ ] 数据库表结构和索引优化完成，支持高效的文件树查询
- [ ] FileNode数据模型类实现完成，支持所有文件操作
- [ ] FileService服务层实现完成，支持业务逻辑处理
- [ ] 文件内容读取和静态资源访问功能正常
- [ ] 面包屑导航数据生成功能正常
- [ ] 更新后端指南文档 (`/docs/architecture/Backend_Architecture_and_Guide.md`)

### 相关文件
- `backend/src/models/FileNode.js` - 文件节点数据模型 (创建)
- `backend/src/services/fileService.js` - 文件业务逻辑服务 (创建)
- `backend/src/config/database.js` - 数据库配置 (更新)
- `/docs/architecture/Backend_Architecture_and_Guide.md` - 后端架构指南 (更新)

---

## 任务3.2: 后端API开发与测试闭环 (Backend Loop)

**优先级**: P0 (最高)
**预估时间**: 10-12小时
**负责人**: Alex (工程师)
**依赖任务**: 任务3.1 (环境与数据模型准备)

### 任务描述
严格按照任务3.0的API契约，实现所有文件管理相关的API接口。采用TDD开发模式，先写测试，再写实现，确保代码质量和业务逻辑正确性。

### 具体实施步骤

#### 3.2.1 创建文件控制器 (TDD方式)

**第一步：编写控制器测试**
```javascript
// tests/integration/fileController.test.js
const request = require('supertest');
const app = require('../../src/app');

describe('File Controller', () => {
  let testSubjectId;
  let testFileId;

  beforeEach(async () => {
    // 创建测试数据
    testSubjectId = await createTestSubject({ name: '测试学科' });
    testFileId = await createTestFile({
      subject_id: testSubjectId,
      name: 'test.md',
      type: 'file',
      content: '# 测试文件\n\n这是测试内容。'
    });
  });

  afterEach(async () => {
    // 清理测试数据
    await clearTestData();
  });

  describe('GET /api/v1/subjects/:id/files', () => {
    test('should return subject file tree', async () => {
      const response = await request(app.callback())
        .get(`/api/v1/subjects/${testSubjectId}/files`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        code: 200,
        message: '获取成功'
      });

      expect(response.body.data.subject).toMatchObject({
        id: testSubjectId,
        name: '测试学科'
      });

      expect(Array.isArray(response.body.data.file_tree)).toBe(true);
    });

    test('should return 404 for non-existent subject', async () => {
      const response = await request(app.callback())
        .get('/api/v1/subjects/999/files')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        code: 404,
        message: '学科不存在',
        error: 'SUBJECT_NOT_FOUND'
      });
    });
  });

  describe('GET /api/v1/files/:id', () => {
    test('should return file content with breadcrumb', async () => {
      const response = await request(app.callback())
        .get(`/api/v1/files/${testFileId}`)
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        code: 200,
        message: '获取成功'
      });

      expect(response.body.data.file).toMatchObject({
        id: testFileId,
        name: 'test.md',
        type: 'file'
      });

      expect(response.body.data.content).toContain('# 测试文件');
      expect(Array.isArray(response.body.data.breadcrumb)).toBe(true);
    });

    test('should return 404 for non-existent file', async () => {
      const response = await request(app.callback())
        .get('/api/v1/files/999')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        code: 404,
        message: '文件不存在'
      });
    });
  });

  describe('GET /api/v1/subjects/public', () => {
    test('should return public subjects list', async () => {
      const response = await request(app.callback())
        .get('/api/v1/subjects/public')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        code: 200,
        message: '获取成功'
      });

      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);

      const subject = response.body.data.find(s => s.id === testSubjectId);
      expect(subject).toMatchObject({
        id: testSubjectId,
        name: '测试学科'
      });
    });
  });
});
```

**第二步：实现文件控制器**
```javascript
// backend/src/controllers/fileController.js
const FileService = require('../services/fileService');
const FileNode = require('../models/FileNode');
const Subject = require('../models/Subject');

class FileController {
  constructor() {
    this.fileService = new FileService(
      new FileNode(global.db),
      new Subject(global.db)
    );
  }

  async getSubjectFileTree(ctx) {
    try {
      const { id } = ctx.params;
      const result = await this.fileService.getSubjectFileTree(parseInt(id));

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '获取成功',
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async getFileContent(ctx) {
    try {
      const { id } = ctx.params;
      const result = await this.fileService.getFileContent(parseInt(id));

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '获取成功',
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async getAssetFile(ctx) {
    try {
      const { fileNodeId } = ctx.params;
      const result = await this.fileService.getAssetFile(parseInt(fileNodeId));

      const fs = require('fs');
      const path = require('path');
      const mime = require('mime-types');

      // 设置响应头
      const mimeType = mime.lookup(result.path) || 'application/octet-stream';
      ctx.type = mimeType;
      ctx.set('Cache-Control', 'public, max-age=31536000'); // 1年缓存
      ctx.set('ETag', `"${result.stats.mtime.getTime()}"`);
      ctx.set('Last-Modified', result.stats.mtime.toUTCString());

      // 检查If-None-Match头
      if (ctx.headers['if-none-match'] === `"${result.stats.mtime.getTime()}"`) {
        ctx.status = 304;
        return;
      }

      // 返回文件内容
      ctx.body = fs.createReadStream(result.path);
    } catch (error) {
      throw error;
    }
  }

  async getPublicSubjects(ctx) {
    try {
      const subjects = await this.fileService.getPublicSubjects();

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '获取成功',
        data: subjects,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  async searchFiles(ctx) {
    try {
      const { q: query, subject_id } = ctx.query;
      const result = await this.fileService.searchFiles(
        query,
        subject_id ? parseInt(subject_id) : null
      );

      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '搜索完成',
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }
}

module.exports = FileController;
```
```
