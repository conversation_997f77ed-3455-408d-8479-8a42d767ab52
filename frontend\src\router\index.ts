import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/HomePage.vue'),
    meta: {
      title: '期末复习平台'
    }
  },
  {
    path: '/subjects/:id',
    name: 'SubjectDetail',
    component: () => import('@/views/SubjectDetail.vue'),
    meta: {
      title: '学科详情'
    }
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('@/views/AdminPanel.vue'),
    meta: {
      title: '管理后台',
      requiresAdmin: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }
  
  // 管理员权限检查 (暂时跳过，后续实现)
  if (to.meta?.requiresAdmin) {
    // TODO: 实现管理员权限检查
    console.log('管理员权限检查 - 暂时跳过')
  }
  
  next()
})

export default router
