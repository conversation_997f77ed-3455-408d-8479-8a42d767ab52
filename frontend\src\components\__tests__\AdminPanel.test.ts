import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { message } from 'ant-design-vue'
import AdminPanel from '@/views/AdminPanel.vue'
import { ApiService } from '@/services/api'

// Mock API Service
vi.mock('@/services/api', () => ({
  ApiService: {
    getSubjects: vi.fn(),
    createSubject: vi.fn(),
    deleteSubject: vi.fn(),
    initDatabase: vi.fn()
  }
}))

// Mock ant-design-vue message
vi.mock('ant-design-vue', async () => {
  const actual = await vi.importActual('ant-design-vue')
  return {
    ...actual,
    message: {
      success: vi.fn(),
      error: vi.fn()
    }
  }
})

describe('AdminPanel.vue', () => {
  let wrapper: any
  let router: any

  beforeEach(() => {
    // 创建路由实例
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', component: { template: '<div>Home</div>' } },
        { path: '/admin', component: { template: '<div>Admin</div>' } },
        { path: '/subjects/:id', component: { template: '<div>Subject Detail</div>' } }
      ]
    })

    // 重置所有mock
    vi.clearAllMocks()
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  it('应该正确渲染管理后台标题', async () => {
    // Mock API响应
    vi.mocked(ApiService.getSubjects).mockResolvedValue({
      success: true,
      code: 200,
      message: '获取学科列表成功',
      data: [],
      timestamp: new Date().toISOString()
    })

    wrapper = mount(AdminPanel, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    expect(wrapper.find('.title').text()).toBe('管理后台')
  })

  it('应该在组件挂载时加载学科列表', async () => {
    const mockSubjects = [
      {
        id: 1,
        name: '高等数学',
        description: '高等数学复习资料',
        created_at: '2025-01-08T10:00:00.000Z',
        updated_at: '2025-01-08T10:00:00.000Z'
      }
    ]

    vi.mocked(ApiService.getSubjects).mockResolvedValue({
      success: true,
      code: 200,
      message: '获取学科列表成功',
      data: mockSubjects,
      timestamp: new Date().toISOString()
    })

    wrapper = mount(AdminPanel, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))

    expect(ApiService.getSubjects).toHaveBeenCalled()
    expect(wrapper.vm.subjects).toEqual(mockSubjects)
  })

  it('应该能够显示创建学科模态框', async () => {
    vi.mocked(ApiService.getSubjects).mockResolvedValue({
      success: true,
      code: 200,
      message: '获取学科列表成功',
      data: [],
      timestamp: new Date().toISOString()
    })

    wrapper = mount(AdminPanel, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    // 点击新建学科按钮
    const createButton = wrapper.find('button:contains("新建学科")')
    await createButton.trigger('click')

    expect(wrapper.vm.createModalVisible).toBe(true)
  })

  it('应该能够创建新学科', async () => {
    vi.mocked(ApiService.getSubjects).mockResolvedValue({
      success: true,
      code: 200,
      message: '获取学科列表成功',
      data: [],
      timestamp: new Date().toISOString()
    })

    const mockCreatedSubject = {
      id: 1,
      name: '测试学科',
      description: '测试描述',
      created_at: '2025-01-08T10:00:00.000Z',
      updated_at: '2025-01-08T10:00:00.000Z'
    }

    vi.mocked(ApiService.createSubject).mockResolvedValue({
      success: true,
      code: 201,
      message: '学科创建成功',
      data: mockCreatedSubject,
      timestamp: new Date().toISOString()
    })

    wrapper = mount(AdminPanel, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    // 设置表单数据
    wrapper.vm.createForm.name = '测试学科'
    wrapper.vm.createForm.description = '测试描述'
    wrapper.vm.createModalVisible = true

    await wrapper.vm.$nextTick()

    // 模拟表单验证通过
    wrapper.vm.$refs.createFormRef = {
      validate: vi.fn().mockResolvedValue(true)
    }

    // 调用创建方法
    await wrapper.vm.handleCreate()

    expect(ApiService.createSubject).toHaveBeenCalledWith({
      name: '测试学科',
      description: '测试描述'
    })
    expect(message.success).toHaveBeenCalledWith('学科创建成功')
    expect(wrapper.vm.createModalVisible).toBe(false)
  })

  it('应该能够删除学科', async () => {
    const mockSubjects = [
      {
        id: 1,
        name: '高等数学',
        description: '高等数学复习资料',
        created_at: '2025-01-08T10:00:00.000Z',
        updated_at: '2025-01-08T10:00:00.000Z'
      }
    ]

    vi.mocked(ApiService.getSubjects).mockResolvedValue({
      success: true,
      code: 200,
      message: '获取学科列表成功',
      data: mockSubjects,
      timestamp: new Date().toISOString()
    })

    vi.mocked(ApiService.deleteSubject).mockResolvedValue({
      success: true,
      code: 200,
      message: '学科删除成功',
      data: {
        deleted_id: 1,
        deleted_files_count: 5
      },
      timestamp: new Date().toISOString()
    })

    wrapper = mount(AdminPanel, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))

    // 调用删除方法
    await wrapper.vm.deleteSubject(1)

    expect(ApiService.deleteSubject).toHaveBeenCalledWith(1)
    expect(message.success).toHaveBeenCalledWith('学科删除成功，同时删除了 5 个文件')
  })

  it('应该能够初始化数据库', async () => {
    vi.mocked(ApiService.getSubjects).mockResolvedValue({
      success: true,
      code: 200,
      message: '获取学科列表成功',
      data: [],
      timestamp: new Date().toISOString()
    })

    vi.mocked(ApiService.initDatabase).mockResolvedValue({
      success: true,
      code: 200,
      message: '数据库初始化成功',
      data: {
        tables_created: ['subjects', 'file_nodes', 'operation_logs'],
        sample_data_inserted: true
      },
      timestamp: new Date().toISOString()
    })

    wrapper = mount(AdminPanel, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    // 调用初始化数据库方法
    await wrapper.vm.initDatabase()

    expect(ApiService.initDatabase).toHaveBeenCalled()
    expect(message.success).toHaveBeenCalledWith('数据库初始化成功')
  })

  it('应该正确处理API错误', async () => {
    const errorMessage = '网络错误'
    vi.mocked(ApiService.getSubjects).mockRejectedValue(new Error(errorMessage))

    wrapper = mount(AdminPanel, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))

    expect(message.error).toHaveBeenCalledWith(errorMessage)
  })

  it('应该正确格式化日期', async () => {
    vi.mocked(ApiService.getSubjects).mockResolvedValue({
      success: true,
      code: 200,
      message: '获取学科列表成功',
      data: [],
      timestamp: new Date().toISOString()
    })

    wrapper = mount(AdminPanel, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    const testDate = '2025-01-08T10:00:00.000Z'
    const formattedDate = wrapper.vm.formatDate(testDate)

    expect(formattedDate).toBe(new Date(testDate).toLocaleString('zh-CN'))
  })

  it('应该验证表单输入', async () => {
    vi.mocked(ApiService.getSubjects).mockResolvedValue({
      success: true,
      code: 200,
      message: '获取学科列表成功',
      data: [],
      timestamp: new Date().toISOString()
    })

    wrapper = mount(AdminPanel, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    // 检查表单验证规则
    const rules = wrapper.vm.createRules
    
    expect(rules.name).toBeDefined()
    expect(rules.name[0].required).toBe(true)
    expect(rules.name[1].min).toBe(1)
    expect(rules.name[1].max).toBe(50)
    expect(rules.name[2].pattern).toEqual(/^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/)
  })
})
