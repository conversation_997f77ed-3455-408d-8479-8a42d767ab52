import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import axios from 'axios'
import { ApiService, type Subject, type CreateSubjectRequest } from '../api'

// Mock axios
vi.mock('axios')
const mockedAxios = vi.mocked(axios)

describe('ApiService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock axios.create
    mockedAxios.create = vi.fn(() => ({
      get: vi.fn(),
      post: vi.fn(),
      delete: vi.fn(),
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() }
      }
    })) as any
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('checkHealth', () => {
    it('应该返回健康检查结果', async () => {
      const mockResponse = {
        data: {
          success: true,
          code: 200,
          message: '系统健康',
          data: {
            status: 'healthy',
            timestamp: '2025-01-08T10:00:00.000Z',
            version: '1.0.0',
            database: 'connected',
            response_time_ms: 10,
            environment: 'test'
          },
          timestamp: '2025-01-08T10:00:00.000Z'
        }
      }

      const mockAxiosInstance = {
        get: vi.fn().mockResolvedValue(mockResponse),
        post: vi.fn(),
        delete: vi.fn(),
        interceptors: {
          request: { use: vi.fn() },
          response: { use: vi.fn() }
        }
      }

      mockedAxios.create = vi.fn().mockReturnValue(mockAxiosInstance)

      // 重新导入模块以使用新的mock
      const { ApiService: TestApiService } = await import('../api')
      
      const result = await TestApiService.checkHealth()

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/health')
      expect(result).toEqual(mockResponse.data)
      expect(result.data.status).toBe('healthy')
    })
  })

  describe('getSubjects', () => {
    it('应该返回学科列表', async () => {
      const mockSubjects: Subject[] = [
        {
          id: 1,
          name: '高等数学',
          description: '高等数学复习资料',
          created_at: '2025-01-08T10:00:00.000Z',
          updated_at: '2025-01-08T10:00:00.000Z'
        },
        {
          id: 2,
          name: '线性代数',
          description: '线性代数复习资料',
          created_at: '2025-01-08T10:00:00.000Z',
          updated_at: '2025-01-08T10:00:00.000Z'
        }
      ]

      const mockResponse = {
        data: {
          success: true,
          code: 200,
          message: '获取学科列表成功',
          data: mockSubjects,
          timestamp: '2025-01-08T10:00:00.000Z'
        }
      }

      const mockAxiosInstance = {
        get: vi.fn().mockResolvedValue(mockResponse),
        post: vi.fn(),
        delete: vi.fn(),
        interceptors: {
          request: { use: vi.fn() },
          response: { use: vi.fn() }
        }
      }

      mockedAxios.create = vi.fn().mockReturnValue(mockAxiosInstance)

      const { ApiService: TestApiService } = await import('../api')
      
      const result = await TestApiService.getSubjects()

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/subjects')
      expect(result).toEqual(mockResponse.data)
      expect(result.data).toHaveLength(2)
      expect(result.data[0].name).toBe('高等数学')
    })
  })

  describe('getSubject', () => {
    it('应该返回指定学科详情', async () => {
      const mockSubject: Subject = {
        id: 1,
        name: '高等数学',
        description: '高等数学复习资料',
        created_at: '2025-01-08T10:00:00.000Z',
        updated_at: '2025-01-08T10:00:00.000Z'
      }

      const mockResponse = {
        data: {
          success: true,
          code: 200,
          message: '获取学科详情成功',
          data: mockSubject,
          timestamp: '2025-01-08T10:00:00.000Z'
        }
      }

      const mockAxiosInstance = {
        get: vi.fn().mockResolvedValue(mockResponse),
        post: vi.fn(),
        delete: vi.fn(),
        interceptors: {
          request: { use: vi.fn() },
          response: { use: vi.fn() }
        }
      }

      mockedAxios.create = vi.fn().mockReturnValue(mockAxiosInstance)

      const { ApiService: TestApiService } = await import('../api')
      
      const result = await TestApiService.getSubject(1)

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/subjects/1')
      expect(result).toEqual(mockResponse.data)
      expect(result.data.id).toBe(1)
      expect(result.data.name).toBe('高等数学')
    })
  })

  describe('createSubject', () => {
    it('应该创建新学科', async () => {
      const createRequest: CreateSubjectRequest = {
        name: '概率论',
        description: '概率论与数理统计'
      }

      const mockCreatedSubject: Subject = {
        id: 3,
        name: '概率论',
        description: '概率论与数理统计',
        created_at: '2025-01-08T10:00:00.000Z',
        updated_at: '2025-01-08T10:00:00.000Z'
      }

      const mockResponse = {
        data: {
          success: true,
          code: 201,
          message: '学科创建成功',
          data: mockCreatedSubject,
          timestamp: '2025-01-08T10:00:00.000Z'
        }
      }

      const mockAxiosInstance = {
        get: vi.fn(),
        post: vi.fn().mockResolvedValue(mockResponse),
        delete: vi.fn(),
        interceptors: {
          request: { use: vi.fn() },
          response: { use: vi.fn() }
        }
      }

      mockedAxios.create = vi.fn().mockReturnValue(mockAxiosInstance)

      const { ApiService: TestApiService } = await import('../api')
      
      const result = await TestApiService.createSubject(createRequest)

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/subjects', createRequest)
      expect(result).toEqual(mockResponse.data)
      expect(result.data.id).toBe(3)
      expect(result.data.name).toBe('概率论')
    })
  })

  describe('deleteSubject', () => {
    it('应该删除指定学科', async () => {
      const mockResponse = {
        data: {
          success: true,
          code: 200,
          message: '学科删除成功',
          data: {
            deleted_id: 1,
            deleted_files_count: 5
          },
          timestamp: '2025-01-08T10:00:00.000Z'
        }
      }

      const mockAxiosInstance = {
        get: vi.fn(),
        post: vi.fn(),
        delete: vi.fn().mockResolvedValue(mockResponse),
        interceptors: {
          request: { use: vi.fn() },
          response: { use: vi.fn() }
        }
      }

      mockedAxios.create = vi.fn().mockReturnValue(mockAxiosInstance)

      const { ApiService: TestApiService } = await import('../api')
      
      const result = await TestApiService.deleteSubject(1)

      expect(mockAxiosInstance.delete).toHaveBeenCalledWith('/subjects/1')
      expect(result).toEqual(mockResponse.data)
      expect(result.data.deleted_id).toBe(1)
      expect(result.data.deleted_files_count).toBe(5)
    })
  })

  describe('initDatabase', () => {
    it('应该初始化数据库', async () => {
      const mockResponse = {
        data: {
          success: true,
          code: 200,
          message: '数据库初始化成功',
          data: {
            tables_created: ['subjects', 'file_nodes', 'operation_logs'],
            sample_data_inserted: true
          },
          timestamp: '2025-01-08T10:00:00.000Z'
        }
      }

      const mockAxiosInstance = {
        get: vi.fn(),
        post: vi.fn().mockResolvedValue(mockResponse),
        delete: vi.fn(),
        interceptors: {
          request: { use: vi.fn() },
          response: { use: vi.fn() }
        }
      }

      mockedAxios.create = vi.fn().mockReturnValue(mockAxiosInstance)

      const { ApiService: TestApiService } = await import('../api')
      
      const result = await TestApiService.initDatabase()

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/dev/init-database')
      expect(result).toEqual(mockResponse.data)
      expect(result.data.tables_created).toContain('subjects')
    })
  })
})
