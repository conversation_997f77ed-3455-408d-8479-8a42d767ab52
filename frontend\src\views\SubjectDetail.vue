<template>
  <div class="subject-detail">
    <a-layout>
      <a-layout-header class="header">
        <div class="header-content">
          <a-space>
            <a-button @click="$router.back()" type="text">
              ← 返回
            </a-button>
            <h1 class="title">{{ subject?.name || '学科详情' }}</h1>
          </a-space>
        </div>
      </a-layout-header>
      
      <a-layout-content class="content">
        <div class="content-wrapper">
          <a-card :loading="loading">
            <div v-if="subject">
              <h2>{{ subject.name }}</h2>
              <p v-if="subject.description">{{ subject.description }}</p>
              <p v-else class="no-description">暂无描述</p>
              
              <a-divider />
              
              <a-descriptions title="学科信息" :column="2">
                <a-descriptions-item label="创建时间">
                  {{ formatDate(subject.created_at) }}
                </a-descriptions-item>
                <a-descriptions-item label="更新时间">
                  {{ formatDate(subject.updated_at) }}
                </a-descriptions-item>
              </a-descriptions>
              
              <a-divider />
              
              <a-alert 
                message="功能开发中" 
                description="文件浏览和Markdown渲染功能将在后续Sprint中实现" 
                type="info" 
                show-icon 
              />
            </div>
            
            <a-result
              v-else-if="!loading"
              status="404"
              title="学科不存在"
              sub-title="请检查学科ID是否正确"
            >
              <template #extra>
                <a-button type="primary" @click="$router.push('/')">
                  返回首页
                </a-button>
              </template>
            </a-result>
          </a-card>
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { ApiService, type Subject } from '@/services/api'

const route = useRoute()
const loading = ref(false)
const subject = ref<Subject | null>(null)

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 加载学科详情
const loadSubject = async () => {
  try {
    loading.value = true
    const id = parseInt(route.params.id as string)
    
    if (isNaN(id)) {
      message.error('无效的学科ID')
      return
    }
    
    const response = await ApiService.getSubject(id)
    
    if (response.success) {
      subject.value = response.data
    } else {
      message.error(response.message || '加载学科详情失败')
    }
  } catch (error: any) {
    console.error('加载学科详情失败:', error)
    message.error(error.message || '网络错误')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadSubject()
})
</script>

<style scoped>
.subject-detail {
  min-height: 100vh;
}

.header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  height: 100%;
}

.title {
  margin: 0;
  color: #1890ff;
  font-size: 20px;
  font-weight: 600;
}

.content {
  padding: 24px;
  background: #f5f5f5;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.no-description {
  color: #999;
  font-style: italic;
}
</style>
