import { defineConfig } from 'unocss'

export default defineConfig({
  // 预设
  presets: [],
  
  // 自定义规则
  rules: [
    // 间距工具类
    [/^m-(\d+)$/, ([, d]) => ({ margin: `${d}px` })],
    [/^p-(\d+)$/, ([, d]) => ({ padding: `${d}px` })],
    [/^mt-(\d+)$/, ([, d]) => ({ 'margin-top': `${d}px` })],
    [/^mb-(\d+)$/, ([, d]) => ({ 'margin-bottom': `${d}px` })],
    [/^ml-(\d+)$/, ([, d]) => ({ 'margin-left': `${d}px` })],
    [/^mr-(\d+)$/, ([, d]) => ({ 'margin-right': `${d}px` })],
    [/^pt-(\d+)$/, ([, d]) => ({ 'padding-top': `${d}px` })],
    [/^pb-(\d+)$/, ([, d]) => ({ 'padding-bottom': `${d}px` })],
    [/^pl-(\d+)$/, ([, d]) => ({ 'padding-left': `${d}px` })],
    [/^pr-(\d+)$/, ([, d]) => ({ 'padding-right': `${d}px` })],
    
    // 文本对齐
    ['text-center', { 'text-align': 'center' }],
    ['text-left', { 'text-align': 'left' }],
    ['text-right', { 'text-align': 'right' }],
    
    // 显示/隐藏
    ['hidden', { display: 'none' }],
    ['block', { display: 'block' }],
    ['inline', { display: 'inline' }],
    ['inline-block', { display: 'inline-block' }],
    ['flex', { display: 'flex' }],
    ['grid', { display: 'grid' }],
    
    // Flex布局
    ['flex-center', { display: 'flex', 'align-items': 'center', 'justify-content': 'center' }],
    ['flex-col', { 'flex-direction': 'column' }],
    ['flex-row', { 'flex-direction': 'row' }],
    ['items-center', { 'align-items': 'center' }],
    ['justify-center', { 'justify-content': 'center' }],
    ['justify-between', { 'justify-content': 'space-between' }],
    
    // 宽高
    ['w-full', { width: '100%' }],
    ['h-full', { height: '100%' }],
    ['min-h-screen', { 'min-height': '100vh' }],
    
    // 圆角
    [/^rounded-(\d+)$/, ([, d]) => ({ 'border-radius': `${d}px` })],
    ['rounded', { 'border-radius': '4px' }],
    ['rounded-full', { 'border-radius': '50%' }],
    
    // 阴影
    ['shadow', { 'box-shadow': '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)' }],
    ['shadow-lg', { 'box-shadow': '0 10px 25px rgba(0, 0, 0, 0.15)' }],
    
    // 过渡动画
    ['transition', { transition: 'all 0.3s ease' }],
    ['transition-fast', { transition: 'all 0.15s ease' }],
    ['transition-slow', { transition: 'all 0.5s ease' }]
  ],
  
  // 快捷方式
  shortcuts: {
    'btn': 'px-4 py-2 rounded bg-blue-500 text-white hover:bg-blue-600 transition',
    'card': 'bg-white rounded shadow p-4',
    'input': 'border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-blue-500'
  },
  
  // 主题
  theme: {
    colors: {
      primary: '#1890ff',
      success: '#52c41a',
      warning: '#faad14',
      error: '#f5222d',
      gray: {
        50: '#fafafa',
        100: '#f5f5f5',
        200: '#e8e8e8',
        300: '#d9d9d9',
        400: '#bfbfbf',
        500: '#8c8c8c',
        600: '#595959',
        700: '#434343',
        800: '#262626',
        900: '#1f1f1f'
      }
    },
    spacing: {
      xs: '4px',
      sm: '8px',
      md: '16px',
      lg: '24px',
      xl: '32px',
      '2xl': '48px'
    }
  }
})
