const Koa = require('koa');
const Router = require('koa-router');
const bodyParser = require('koa-bodyparser');
const cors = require('@koa/cors');
const serve = require('koa-static');
const path = require('path');

// 导入数据库配置
const { dbManager, checkConnection } = require('./config/database');

// 导入路由
const healthRouter = require('./routes/health');
const subjectRouter = require('./routes/subjects');
const devRouter = require('./routes/dev');

// 导入中间件
const errorHandler = require('./middleware/errorHandler');
const logger = require('./middleware/logger');

// 创建Koa应用实例
const app = new Koa();
const router = new Router();

// 应用配置
const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || 'development';

// 立即初始化数据库（确保在路由注册前完成）
// 在测试环境中，数据库初始化由测试设置文件处理
if (NODE_ENV !== 'test') {
  try {
    console.log('正在初始化数据库...');
    dbManager.initialize();

    if (!checkConnection()) {
      throw new Error('数据库连接失败');
    }

    console.log('数据库连接成功');
  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  }
}

// 全局错误处理中间件
app.use(errorHandler);

// 请求日志中间件
app.use(logger);

// CORS中间件
app.use(cors({
  origin: NODE_ENV === 'development' ? 'http://localhost:5173' : false,
  credentials: true,
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'Accept']
}));

// 请求体解析中间件
app.use(bodyParser({
  jsonLimit: '10mb',
  formLimit: '10mb',
  textLimit: '10mb'
}));

// 静态文件服务 (用于前端构建文件)
if (NODE_ENV === 'production') {
  app.use(serve(path.join(__dirname, '../public')));
}

// API路由前缀
const apiRouter = new Router({ prefix: '/api/v1' });

// 注册API路由
apiRouter.use(healthRouter.routes(), healthRouter.allowedMethods());
apiRouter.use(subjectRouter.routes(), subjectRouter.allowedMethods());

// 开发环境专用路由
if (NODE_ENV === 'development' || NODE_ENV === 'test') {
  apiRouter.use('/dev', devRouter.routes(), devRouter.allowedMethods());
}

// 应用API路由
app.use(apiRouter.routes());
app.use(apiRouter.allowedMethods());

// 404处理
app.use(async (ctx) => {
  ctx.status = 404;
  ctx.body = {
    success: false,
    code: 404,
    message: '请求的资源不存在',
    error: `路径 ${ctx.path} 未找到`,
    timestamp: new Date().toISOString()
  };
});

// 应用启动函数
async function startServer() {
  try {
    // 数据库已在模块加载时初始化

    // 启动HTTP服务器
    const server = app.listen(PORT, () => {
      console.log(`🚀 服务器启动成功!`);
      console.log(`📍 环境: ${NODE_ENV}`);
      console.log(`🌐 地址: http://localhost:${PORT}`);
      console.log(`📚 API文档: http://localhost:${PORT}/api/v1/health`);
      console.log(`⏰ 启动时间: ${new Date().toISOString()}`);
    });

    // 优雅关闭处理
    const gracefulShutdown = (signal) => {
      console.log(`\n收到 ${signal} 信号，正在优雅关闭服务器...`);

      server.close(() => {
        console.log('HTTP服务器已关闭');

        // 关闭数据库连接
        dbManager.close();

        console.log('应用已完全关闭');
        process.exit(0);
      });

      // 强制关闭超时
      setTimeout(() => {
        console.error('强制关闭应用');
        process.exit(1);
      }, 10000);
    };

    // 监听进程信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    return server;
  } catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，则启动服务器
if (require.main === module) {
  startServer();
}

module.exports = app;
