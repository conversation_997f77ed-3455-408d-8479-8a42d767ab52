const { dbManager } = require('../src/config/database');

// 测试环境配置
process.env.NODE_ENV = 'test';
process.env.DATABASE_PATH = ':memory:'; // 使用内存数据库进行测试

// 全局测试设置
beforeAll(async () => {
  // 确保数据库已初始化（如果app.js还没有初始化的话）
  if (!dbManager.isInitialized) {
    dbManager.initialize();

    // 创建表结构
    const { createTables } = require('../src/config/database');
    createTables();
  }

  console.log('测试环境初始化完成');
});

// 每个测试前清理数据
beforeEach(async () => {
  const db = dbManager.getDatabase();

  // 清理所有表数据
  db.exec('DELETE FROM operation_logs');
  db.exec('DELETE FROM file_nodes');
  db.exec('DELETE FROM subjects');

  // 重置自增ID
  db.exec('DELETE FROM sqlite_sequence WHERE name IN ("subjects", "file_nodes", "operation_logs")');
});

// 全局测试清理
afterAll(async () => {
  // 关闭数据库连接
  dbManager.close();
  console.log('测试环境清理完成');
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});
