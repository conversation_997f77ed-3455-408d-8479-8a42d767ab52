const Router = require('koa-router');
const { checkConnection } = require('../config/database');

const router = new Router();

// GET /api/v1/health - 健康检查API
router.get('/health', async (ctx) => {
  try {
    const startTime = Date.now();

    // 检查数据库连接
    const dbConnected = checkConnection();

    const responseTime = Date.now() - startTime;

    if (dbConnected) {
      ctx.status = 200;
      ctx.body = {
        success: true,
        code: 200,
        message: '系统运行正常',
        data: {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          version: '1.0.0',
          database: 'connected',
          response_time_ms: responseTime,
          environment: process.env.NODE_ENV || 'development'
        },
        timestamp: new Date().toISOString()
      };
    } else {
      ctx.status = 503;
      ctx.body = {
        success: false,
        code: 503,
        message: '服务不可用',
        error: '数据库连接失败',
        timestamp: new Date().toISOString()
      };
    }
  } catch (error) {
    console.error('健康检查失败:', error);

    ctx.status = 503;
    ctx.body = {
      success: false,
      code: 503,
      message: '服务不可用',
      error: '系统内部错误',
      timestamp: new Date().toISOString()
    };
  }
});

module.exports = router;
