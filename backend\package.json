{"name": "term-review-backend", "version": "1.0.0", "description": "期末复习平台后端API服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "init-db": "node src/scripts/init-database.js"}, "keywords": ["term-review", "education", "markdown", "notes"], "author": "<PERSON> (工程师)", "license": "MIT", "dependencies": {"@koa/cors": "^5.0.0", "@koa/multer": "^3.0.2", "better-sqlite3": "^12.2.0", "highlight.js": "^11.9.0", "joi": "^17.11.0", "koa": "^2.14.2", "koa-bodyparser": "^4.4.1", "koa-router": "^12.0.1", "koa-static": "^5.0.0", "marked": "^12.0.0", "multer": "^1.4.5-lts.1", "sqlite3": "^5.1.7", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.8", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}