<template>
  <div class="home-page">
    <a-layout>
      <a-layout-header class="header">
        <div class="header-content">
          <h1 class="title">期末复习平台</h1>
          <a-space>
            <a-button type="primary" @click="checkHealth">
              健康检查
            </a-button>
            <a-button @click="$router.push('/admin')">
              管理后台
            </a-button>
          </a-space>
        </div>
      </a-layout-header>
      
      <a-layout-content class="content">
        <div class="content-wrapper">
          <a-card title="学科列表" :loading="loading">
            <template #extra>
              <a-button @click="loadSubjects" :loading="loading">
                刷新
              </a-button>
            </template>
            
            <div v-if="subjects.length === 0 && !loading" class="empty-state">
              <a-empty description="暂无学科数据">
                <a-button type="primary" @click="$router.push('/admin')">
                  去管理后台添加学科
                </a-button>
              </a-empty>
            </div>
            
            <a-row :gutter="[16, 16]" v-else>
              <a-col 
                v-for="subject in subjects" 
                :key="subject.id"
                :xs="24" :sm="12" :md="8" :lg="6"
              >
                <a-card 
                  :title="subject.name"
                  hoverable
                  @click="$router.push(`/subjects/${subject.id}`)"
                  class="subject-card"
                >
                  <p v-if="subject.description">{{ subject.description }}</p>
                  <p v-else class="no-description">暂无描述</p>
                  <template #actions>
                    <a-button type="link" size="small">
                      查看详情
                    </a-button>
                  </template>
                </a-card>
              </a-col>
            </a-row>
          </a-card>
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { ApiService, type Subject } from '@/services/api'

// 响应式数据
const loading = ref(false)
const subjects = ref<Subject[]>([])

// 加载学科列表
const loadSubjects = async () => {
  try {
    loading.value = true
    const response = await ApiService.getSubjects()
    
    if (response.success) {
      subjects.value = response.data
      message.success(`加载了 ${response.data.length} 个学科`)
    } else {
      message.error(response.message || '加载学科列表失败')
    }
  } catch (error: any) {
    console.error('加载学科列表失败:', error)
    message.error(error.message || '网络错误')
  } finally {
    loading.value = false
  }
}

// 健康检查
const checkHealth = async () => {
  try {
    const response = await ApiService.checkHealth()
    
    if (response.success) {
      message.success(`系统状态: ${response.data.status}`)
    } else {
      message.error(response.message || '健康检查失败')
    }
  } catch (error: any) {
    console.error('健康检查失败:', error)
    message.error(error.message || '网络错误')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadSubjects()
})
</script>

<style scoped>
.home-page {
  min-height: 100vh;
}

.header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.title {
  margin: 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

.content {
  padding: 24px;
  background: #f5f5f5;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.subject-card {
  cursor: pointer;
  transition: all 0.3s;
}

.subject-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.no-description {
  color: #999;
  font-style: italic;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}
</style>
