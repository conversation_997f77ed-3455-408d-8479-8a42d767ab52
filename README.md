# 期末复习平台

基于Markdown的在线笔记管理和分享平台，支持管理员便捷上传管理笔记内容，访客免登录浏览所有公开内容。

## 项目概述

- **项目名称**: 期末复习平台 (Term Review Platform)
- **版本**: v1.0.0
- **开发状态**: Sprint-1 开发中
- **技术架构**: 前后端分离

## 技术栈

### 后端
- **运行环境**: Node.js 18+
- **Web框架**: Koa 2.14+
- **数据库**: SQLite (better-sqlite3 9.0+)
- **参数验证**: Joi 17.11+
- **测试框架**: Jest + Supertest

### 前端
- **框架**: Vue 3.4+ + TypeScript 5.0+
- **构建工具**: Vite 5.0+
- **UI组件**: Ant Design Vue 4.0+
- **状态管理**: Pinia 2.0+
- **路由**: Vue Router 4.0+
- **CSS框架**: UnoCSS 0.58+

## 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm >= 9.0.0

### 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 初始化数据库

```bash
# 方法1: 使用脚本初始化
cd backend
npm run init-db

# 方法2: 启动后端后调用API
# POST http://localhost:3000/api/v1/dev/init-database
```

### 启动开发服务器

```bash
# 启动后端服务 (端口: 3000)
cd backend
npm run dev

# 启动前端服务 (端口: 5173)
cd frontend
npm run dev
```

### 访问应用

- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:3000/api/v1
- **健康检查**: http://localhost:3000/api/v1/health

## API文档

详细的API文档请查看: [API接口契约文档](./docs/architecture/API_Reference.md)

### 核心API接口

- `GET /api/v1/health` - 系统健康检查
- `GET /api/v1/subjects` - 获取所有学科列表
- `POST /api/v1/subjects` - 创建新学科 (管理员)
- `DELETE /api/v1/subjects/:id` - 删除学科 (管理员)
- `POST /api/v1/dev/init-database` - 初始化数据库 (开发专用)

## 项目结构

```
Term Review/
├── docs/                   # 项目文档
│   ├── prd/               # 产品需求文档
│   ├── architecture/      # 架构设计文档
│   ├── development/       # 开发技术文档
│   └── tasks/            # 任务规划文档
├── backend/               # 后端项目
│   ├── src/              # 源代码
│   ├── data/             # 数据文件
│   └── package.json      # 后端依赖
├── frontend/              # 前端项目
│   ├── src/              # 源代码
│   ├── public/           # 静态资源
│   └── package.json      # 前端依赖
└── README.md             # 项目说明
```

## 开发规范

### 代码规范
- 使用 ESLint 进行代码检查
- 遵循统一的代码格式化规范
- 所有API必须100%遵循API契约

### 测试规范
- 单元测试覆盖率 > 90%
- 集成测试覆盖率 > 80%
- E2E测试覆盖率 100%
- 使用Playwright进行所有浏览器相关测试

### Git规范
- 使用语义化提交信息
- 功能开发使用feature分支
- 代码审查后合并到主分支

## 部署说明

### 开发环境
- 后端: `npm run dev` (nodemon热重载)
- 前端: `npm run dev` (Vite热重载)

### 生产环境
- 后端: `npm start` (Node.js直接运行)
- 前端: `npm run build` (构建静态文件)

## 文档链接

- [产品需求文档 (PRD)](./docs/prd/PRD_期末复习平台_v1.0.md)
- [总体架构蓝图](./docs/architecture/Overall_Architecture_期末复习平台.md)
- [API接口契约](./docs/architecture/API_Reference.md)
- [后端架构指南](./docs/architecture/Backend_Architecture_and_Guide.md)
- [更新日志](./docs/CHANGELOG.md)

## 开发团队

- **项目负责人**: Mike (团队领袖)
- **产品经理**: Emma
- **架构师**: Bob
- **工程师**: Alex
- **数据分析师**: David

## 许可证

MIT License

## 更新日志

查看 [CHANGELOG.md](./docs/CHANGELOG.md) 了解详细的版本更新信息。
