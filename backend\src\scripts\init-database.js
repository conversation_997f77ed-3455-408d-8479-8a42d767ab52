#!/usr/bin/env node

/**
 * 数据库初始化脚本
 * 用于创建数据库表结构和插入初始数据
 */

const { createTables, insertSampleData, dbManager } = require('../config/database');

async function initDatabase() {
  try {
    console.log('🚀 开始初始化数据库...');
    
    // 初始化数据库连接
    console.log('📡 正在连接数据库...');
    dbManager.initialize();
    
    // 创建表结构
    console.log('🏗️  正在创建数据库表结构...');
    createTables();
    console.log('✅ 数据库表结构创建成功');
    
    // 插入示例数据
    console.log('📝 正在插入示例数据...');
    const sampleDataInserted = insertSampleData();
    
    if (sampleDataInserted) {
      console.log('✅ 示例数据插入成功');
    } else {
      console.log('ℹ️  数据库已有数据，跳过示例数据插入');
    }
    
    console.log('🎉 数据库初始化完成！');
    
    // 显示数据库状态
    const db = dbManager.getDatabase();
    const tables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `).all();
    
    console.log('\n📊 数据库状态:');
    console.log('表列表:', tables.map(t => t.name).join(', '));
    
    // 显示各表记录数
    for (const table of tables) {
      const count = db.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
      console.log(`  - ${table.name}: ${count.count} 条记录`);
    }
    
    console.log('\n🌐 可以通过以下API测试数据库:');
    console.log('  - GET http://localhost:3000/api/v1/health');
    console.log('  - GET http://localhost:3000/api/v1/subjects');
    console.log('  - POST http://localhost:3000/api/v1/dev/init-database');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    dbManager.close();
    process.exit(0);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}

module.exports = { initDatabase };
