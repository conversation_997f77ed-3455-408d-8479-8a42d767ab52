import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { message } from 'ant-design-vue'
import HomePage from '@/views/HomePage.vue'
import { ApiService } from '@/services/api'

// Mock API Service
vi.mock('@/services/api', () => ({
  ApiService: {
    getSubjects: vi.fn(),
    checkHealth: vi.fn()
  }
}))

// Mock ant-design-vue message
vi.mock('ant-design-vue', async () => {
  const actual = await vi.importActual('ant-design-vue')
  return {
    ...actual,
    message: {
      success: vi.fn(),
      error: vi.fn()
    }
  }
})

describe('HomePage.vue', () => {
  let wrapper: any
  let router: any

  beforeEach(() => {
    // 创建路由实例
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', component: { template: '<div>Home</div>' } },
        { path: '/admin', component: { template: '<div>Admin</div>' } },
        { path: '/subjects/:id', component: { template: '<div>Subject Detail</div>' } }
      ]
    })

    // 重置所有mock
    vi.clearAllMocks()
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  it('应该正确渲染页面标题', async () => {
    // Mock API响应
    vi.mocked(ApiService.getSubjects).mockResolvedValue({
      success: true,
      code: 200,
      message: '获取学科列表成功',
      data: [],
      timestamp: new Date().toISOString()
    })

    wrapper = mount(HomePage, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    expect(wrapper.find('.title').text()).toBe('期末复习平台')
  })

  it('应该在组件挂载时加载学科列表', async () => {
    const mockSubjects = [
      {
        id: 1,
        name: '高等数学',
        description: '高等数学复习资料',
        created_at: '2025-01-08T10:00:00.000Z',
        updated_at: '2025-01-08T10:00:00.000Z'
      }
    ]

    vi.mocked(ApiService.getSubjects).mockResolvedValue({
      success: true,
      code: 200,
      message: '获取学科列表成功',
      data: mockSubjects,
      timestamp: new Date().toISOString()
    })

    wrapper = mount(HomePage, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0)) // 等待异步操作

    expect(ApiService.getSubjects).toHaveBeenCalled()
    expect(wrapper.vm.subjects).toEqual(mockSubjects)
  })

  it('应该显示空状态当没有学科时', async () => {
    vi.mocked(ApiService.getSubjects).mockResolvedValue({
      success: true,
      code: 200,
      message: '获取学科列表成功',
      data: [],
      timestamp: new Date().toISOString()
    })

    wrapper = mount(HomePage, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))

    expect(wrapper.find('.empty-state').exists()).toBe(true)
    expect(wrapper.find('.empty-state').text()).toContain('暂无学科数据')
  })

  it('应该正确处理API错误', async () => {
    const errorMessage = '网络错误'
    vi.mocked(ApiService.getSubjects).mockRejectedValue(new Error(errorMessage))

    wrapper = mount(HomePage, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))

    expect(message.error).toHaveBeenCalledWith(errorMessage)
  })

  it('应该能够执行健康检查', async () => {
    vi.mocked(ApiService.getSubjects).mockResolvedValue({
      success: true,
      code: 200,
      message: '获取学科列表成功',
      data: [],
      timestamp: new Date().toISOString()
    })

    vi.mocked(ApiService.checkHealth).mockResolvedValue({
      success: true,
      code: 200,
      message: '系统健康',
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        database: 'connected',
        response_time_ms: 10,
        environment: 'test'
      },
      timestamp: new Date().toISOString()
    })

    wrapper = mount(HomePage, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    // 点击健康检查按钮
    const healthButton = wrapper.find('button:contains("健康检查")')
    await healthButton.trigger('click')

    expect(ApiService.checkHealth).toHaveBeenCalled()
    expect(message.success).toHaveBeenCalledWith('系统状态: healthy')
  })

  it('应该能够刷新学科列表', async () => {
    vi.mocked(ApiService.getSubjects).mockResolvedValue({
      success: true,
      code: 200,
      message: '获取学科列表成功',
      data: [],
      timestamp: new Date().toISOString()
    })

    wrapper = mount(HomePage, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))

    // 清除之前的调用记录
    vi.clearAllMocks()

    // 点击刷新按钮
    const refreshButton = wrapper.find('button:contains("刷新")')
    await refreshButton.trigger('click')

    expect(ApiService.getSubjects).toHaveBeenCalled()
  })

  it('应该显示加载状态', async () => {
    // 创建一个永不resolve的Promise来模拟加载状态
    vi.mocked(ApiService.getSubjects).mockImplementation(() => new Promise(() => {}))

    wrapper = mount(HomePage, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    expect(wrapper.vm.loading).toBe(true)
  })
})
